package com.fishing.api;

import com.fishing.config.CurrentUser;
import com.fishing.custom.exception.BizException;
import com.fishing.custom.exception.DbException;
import com.fishing.customer.service.IUserService;
import com.fishing.domain.User;
import com.fishing.dto.BasicMessageDto;
import com.fishing.dto.HeadImgUrlDto;
import com.fishing.dto.UpdatePasswordDto;
import com.fishing.oss.OssConfig;
import com.fishing.service.OssService;
import com.fishing.util.SwUtil;
import com.fishing.vo.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @since 2023/10/11 20:08:52
 */
@Tag(name = "用户")
@RestController
@RequestMapping("users")
@RequiredArgsConstructor
public class UserController {

    private static final String FILE_DIRECT = "user-app";
    private final IUserService userService;
    private final OssConfig ossConfig;
    private final OssService ossService;
    private final SwUtil swUtil;

    @PostMapping("password")
    public ResponseEntity<ApiResponse<BasicMessageDto>> updatePassword(@CurrentUser String userPhoneNumber, @RequestBody UpdatePasswordDto dto) throws DbException.UpdateFail, BizException.UpdatePasswordException, BizException.InvalidParam {
        userService.updatePassword(userPhoneNumber, dto);
        return ResponseEntity.ok(ApiResponse.success(new BasicMessageDto("密码修改成功")));
    }

    @PostMapping("head_img")
    public ResponseEntity<ApiResponse<HeadImgUrlDto>> updateHeadImg(@CurrentUser String userPhoneNumber, @RequestPart("file") MultipartFile file) throws DbException.UpdateFail {
        String avatarUrl = ossService.uploadFile(ossConfig.getBucketName(), FILE_DIRECT, file);
        userService.updateHeadImg(userPhoneNumber, avatarUrl);
        return ResponseEntity.ok(ApiResponse.success(new HeadImgUrlDto(avatarUrl)));
    }

    @PostMapping()
    public ResponseEntity<ApiResponse<User>> update(@CurrentUser String userPhoneNumber, @RequestBody User user) throws BizException.InvalidParam, BizException.InvalidOperation {
        if (!StringUtils.hasText(user.getName())) {
            throw new BizException.InvalidParam("昵称不能为空");
        }

        if (!StringUtils.hasText(user.getTitle())) {
            throw new BizException.InvalidParam("称号不能为空");
        }

        swUtil.detect(user.getName());
        swUtil.detect(user.getTitle());

        User updatedUser = userService.update(userPhoneNumber, user);
        return ResponseEntity.ok(ApiResponse.success(updatedUser));
    }

    @GetMapping
    public ResponseEntity<ApiResponse<User>> getUser(@CurrentUser String userPhoneNumber) {
        return ResponseEntity.ok(ApiResponse.success(userService.getUser(userPhoneNumber)));
    }

}
