package com.fishing.api;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fishing.config.CurrentUser;
import com.fishing.custom.exception.BizException;
import com.fishing.customer.service.IUserService;
import com.fishing.domain.User;
import com.fishing.domain.spot.FishingSpot;
import com.fishing.dto.spot.QuerySpotDto;
import com.fishing.dto.spot.SpotCreateDto;
import com.fishing.service.FishingSpotRedisService;
import com.fishing.service.SpotFavoriteService;
import com.fishing.service.impl.FishingSpotService;
import com.fishing.vo.ApiResponse;
import com.fishing.vo.spot.SpotDetailVO;
import com.fishing.vo.spot.SpotPriceVO;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("/api/v1/fishing-spots")
@RequiredArgsConstructor
public class FishingSpotController {

    private final FishingSpotService fishingSpotService;
    private final IUserService userService;
    private final SpotFavoriteService spotFavoriteService;
    private final FishingSpotRedisService fishingSpotRedisService;

    /**
     * 分页获取钓点列表
     *
     * @param querySpotDto 查询条件
     * @return 钓点分页数据
     */
    @PostMapping
    public ResponseEntity<ApiResponse<IPage<SpotDetailVO>>> getFishingSpots(@RequestBody QuerySpotDto querySpotDto) {
        IPage<SpotDetailVO> fishingSpots = fishingSpotService.findFishingSpots(querySpotDto);
        return ResponseEntity.ok(ApiResponse.success(fishingSpots));
    }

    /**
     * 创建钓点
     *
     * @param spotCreateDto 钓点创建数据
     * @return 创建的钓点ID
     */
    @PostMapping("/create")
    public ResponseEntity<ApiResponse<Long>> createFishingSpot(@CurrentUser Long userId, @RequestBody SpotCreateDto spotCreateDto) throws BizException.InvalidOperation {
        User user = userService.getById(userId);
        if (user == null) {
            throw new BizException.InvalidOperation("用户不存在");
        }

        Long spotId = fishingSpotService.createSpot(spotCreateDto, userId);

        FishingSpot spot = fishingSpotService.getById(spotId);
        if (fishingSpotRedisService != null) {
            fishingSpotRedisService.addOrUpdateSpot(spot);
        }
        return ResponseEntity.ok(ApiResponse.success(spotId));
    }

    /**
     * 获取钓点详情
     *
     * @param id 钓点ID
     * @return 钓点详情
     */
    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<SpotDetailVO>> getFishingSpotById(@PathVariable Long id) {
        Optional<SpotDetailVO> spot = fishingSpotService.findById(id);
        return spot.map(s -> ResponseEntity.ok(ApiResponse.success(s)))
                .orElseGet(() -> ResponseEntity.ok(ApiResponse.error(404, "钓点不存在")));
    }

    /**
     * 签到打卡
     *
     * @param id 钓点ID
     * @return 签到结果
     */
    @PostMapping("/{id}/checkin")
    public ResponseEntity<ApiResponse<Boolean>> checkinFishingSpot(@CurrentUser Long userId, @PathVariable Long id) throws BizException.InvalidOperation {
        User user = userService.getById(userId);
        if (user == null) {
            throw new BizException.InvalidOperation("用户不存在");
        }

        boolean success = fishingSpotService.checkin(id, userId);
        if (success) {
            return ResponseEntity.ok(ApiResponse.success(true));
        } else {
            return ResponseEntity.ok(ApiResponse.error(400, "签到失败"));
        }
    }

    /**
     * 获取钓点价格详情
     *
     * @param id 钓点ID
     * @return 价格列表
     */
    @GetMapping("/{id}/prices")
    public ResponseEntity<ApiResponse<List<SpotPriceVO>>> getSpotPrices(@PathVariable Long id) {
        List<SpotPriceVO> prices = fishingSpotService.getPricesBySpotId(id);
        return ResponseEntity.ok(ApiResponse.success(prices));
    }

    /**
     * 更新钓点价格
     *
     * @param id     钓点ID
     * @param prices 价格列表
     * @return 更新结果
     */
    @PostMapping("/{id}/prices")
    public ResponseEntity<ApiResponse<Boolean>> updateSpotPrices(@PathVariable Long id, @RequestBody List<SpotPriceVO> prices) {
        boolean success = fishingSpotService.savePrices(id, prices);
        if (success) {
            return ResponseEntity.ok(ApiResponse.success(true));
        } else {
            return ResponseEntity.ok(ApiResponse.error(400, "更新价格失败"));
        }
    }

    /**
     * 获取用户最近签到的钓点列表
     *
     * @param userId 用户ID
     * @param page   页码 (0-based)
     * @param size   每页数量
     * @return 最近签到钓点列表
     */
    @GetMapping("/user/recent-checkins")
    public ResponseEntity<ApiResponse<List<SpotDetailVO>>> getRecentCheckins(
            @CurrentUser Long userId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) throws BizException.InvalidOperation {
        User user = userService.getById(userId);
        if (user == null) {
            throw new BizException.InvalidOperation("用户不存在");
        }

        List<SpotDetailVO> spots = fishingSpotService.getUserRecentCheckins(userId, page, size);
        return ResponseEntity.ok(ApiResponse.success(spots));
    }

    /**
     * 获取用户收藏的钓点列表
     *
     * @param userId 用户ID
     * @param page   页码 (0-based)
     * @param size   每页数量
     * @return 收藏钓点列表
     */
    @GetMapping("/user/favorites")
    public ResponseEntity<ApiResponse<List<SpotDetailVO>>> getFavorites(
            @CurrentUser Long userId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) throws BizException.InvalidOperation {
        User user = userService.getById(userId);
        if (user == null) {
            throw new BizException.InvalidOperation("用户不存在");
        }

        List<SpotDetailVO> spots = fishingSpotService.getUserFavorites(userId, page, size);
        return ResponseEntity.ok(ApiResponse.success(spots));
    }

    /**
     * 获取用户创建的钓点列表
     *
     * @param userId 用户ID
     * @param page   页码 (0-based)
     * @param size   每页数量
     * @return 用户创建的钓点列表
     */
    @GetMapping("/user/created")
    public ResponseEntity<ApiResponse<List<SpotDetailVO>>> getMyCreatedSpots(
            @CurrentUser Long userId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) throws BizException.InvalidOperation {
        User user = userService.getById(userId);
        if (user == null) {
            throw new BizException.InvalidOperation("用户不存在");
        }

        List<SpotDetailVO> spots = fishingSpotService.getUserCreatedSpots(userId, page, size);
        return ResponseEntity.ok(ApiResponse.success(spots));
    }

    /**
     * 获取附近的钓点列表
     *
     * @param latitude  纬度
     * @param longitude 经度
     * @param radius    搜索半径（公里）
     * @param limit     结果数量限制
     * @return 附近钓点列表
     */
    @GetMapping("/nearby")
    public ResponseEntity<ApiResponse<List<SpotDetailVO>>> getNearbySpots(
            @RequestParam double latitude,
            @RequestParam double longitude,
            @RequestParam(defaultValue = "5.0") double radius,
            @RequestParam(defaultValue = "10") int limit) {
        List<SpotDetailVO> spots = fishingSpotRedisService.findNearbySpots(
                new BigDecimal(latitude),
                new BigDecimal(longitude),
                radius,
                limit
        );
        return ResponseEntity.ok(ApiResponse.success(spots));
    }

    /**
     * 搜索钓点
     *
     * @param query     搜索关键词
     * @param page      页码 (0-based)
     * @param size      每页数量
     * @param latitude  可选纬度（用于地理位置搜索）
     * @param longitude 可选经度（用于地理位置搜索）
     * @param radius    可选搜索半径（公里）
     * @return 搜索结果
     */
    @GetMapping("/search")
    public ResponseEntity<ApiResponse<List<SpotDetailVO>>> searchFishingSpots(
            @RequestParam String query,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) Double latitude,
            @RequestParam(required = false) Double longitude,
            @RequestParam(required = false) Double radius) {

        // 检查地理位置参数是否完整
        boolean hasGeoSearch = latitude != null && longitude != null;

        List<SpotDetailVO> spots;
        if (hasGeoSearch) {
            // 如果提供了地理位置参数，则进行地理位置搜索
            spots = fishingSpotService.searchFishingSpots(
                    query, page, size, latitude, longitude, radius != null ? radius : 50.0);
        } else {
            // 否则进行普通搜索
            spots = fishingSpotService.searchFishingSpots(query, page, size);
        }

        return ResponseEntity.ok(ApiResponse.success(spots));
    }

    /**
     * 收藏钓点
     *
     * @param id 钓点ID
     * @return 收藏结果
     */
    @PostMapping("/{id}/favorite")
    public ResponseEntity<ApiResponse<Boolean>> favoriteFishingSpot(@CurrentUser Long userId, @PathVariable Long id) throws BizException.InvalidOperation {
        User user = userService.getById(userId);
        if (user == null) {
            throw new BizException.InvalidOperation("用户不存在");
        }

        boolean success = spotFavoriteService.addFavorite(id, userId);
        if (success) {
            return ResponseEntity.ok(ApiResponse.success(true));
        } else {
            return ResponseEntity.ok(ApiResponse.error(400, "收藏失败"));
        }
    }

    /**
     * 取消收藏钓点
     *
     * @param id 钓点ID
     * @return 取消收藏结果
     */
    @DeleteMapping("/{id}/favorite")
    public ResponseEntity<ApiResponse<Boolean>> unfavoriteFishingSpot(@CurrentUser Long userId, @PathVariable Long id) throws BizException.InvalidOperation {
        User user = userService.getById(userId);
        if (user == null) {
            throw new BizException.InvalidOperation("用户不存在");
        }

        boolean success = spotFavoriteService.removeFavorite(id, userId);
        if (success) {
            return ResponseEntity.ok(ApiResponse.success(true));
        } else {
            return ResponseEntity.ok(ApiResponse.error(400, "取消收藏失败"));
        }
    }

    /**
     * 检查钓点是否已收藏
     *
     * @param id 钓点ID
     * @return 是否已收藏
     */
    @GetMapping("/{id}/is-favorite")
    public ResponseEntity<ApiResponse<Boolean>> checkIsFavorite(@CurrentUser Long userId, @PathVariable Long id) throws BizException.InvalidOperation {
        User user = userService.getById(userId);
        if (user == null) {
            throw new BizException.InvalidOperation("用户不存在");
        }

        boolean isFavorite = spotFavoriteService.isFavorite(id, userId);
        return ResponseEntity.ok(ApiResponse.success(isFavorite));
    }

    /**
     * 测试获取钓点列表 - 简化版本用于验证动态数据
     *
     * @param pageNum  页码
     * @param pageSize 页大小
     * @return 钓点分页数据
     */
    @GetMapping("/test")
    public ResponseEntity<ApiResponse<IPage<SpotDetailVO>>> testGetFishingSpots(
            @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {

        QuerySpotDto querySpotDto = new QuerySpotDto();
        querySpotDto.setPageNum(pageNum);
        querySpotDto.setPageSize(pageSize);
        querySpotDto.setFilterType(null);
        querySpotDto.setFishTypes(new ArrayList<>());
        querySpotDto.setHasFacilities(null);

        IPage<SpotDetailVO> fishingSpots = fishingSpotService.findFishingSpots(querySpotDto);
        return ResponseEntity.ok(ApiResponse.success(fishingSpots));
    }
}
