package com.fishing.api;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fishing.config.CurrentUser;
import com.fishing.dto.map.AddressDto;
import com.fishing.dto.map.IPInfo;
import com.fishing.dto.map.WeatherInfoDto;
import com.fishing.feign.AMapService;
import com.fishing.vo.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.ResponseEntity;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2023/10/25 14:55:05
 */
@Tag(name = "地图接口")
@RestController
@RequestMapping("/map")
@RequiredArgsConstructor
public class MapController {

    private final AMapService aMapService;
    private final ObjectMapper objectMapper;
    private final RedisTemplate<String, String> redisTemplate;

    @GetMapping("/resolve_coordinate")
    public ResponseEntity<ApiResponse<AddressDto>> resolveCoordinate(@CurrentUser Long userId, String longitude, String latitude) throws JsonProcessingException {
        AddressDto addressByCoordinate = aMapService.getAddressByCoordinate(userId, longitude, latitude);
        cacheUserAddressInfo(addressByCoordinate, userId);
        return ResponseEntity.ok(ApiResponse.success(addressByCoordinate));
    }

    private void cacheUserAddressInfo(AddressDto addressByCoordinate, Long userId) throws JsonProcessingException {
        String addressStr = objectMapper.writeValueAsString(addressByCoordinate);
        redisTemplate.opsForValue().set(userId.toString(), addressStr);
    }

    @GetMapping("/weather_forecast")
    public ResponseEntity<ApiResponse<WeatherInfoDto>> weatherForecast(@CurrentUser Long userId) throws JsonProcessingException {
        AddressDto addressDto = getUserAddressInfoFromCache(userId);
        WeatherInfoDto weather = readWeatherForecastInfoFromCache(addressDto.getAdCode());
        if (weather == null) {
            weather = aMapService.getWeather(addressDto.getAdCode(), true);
        }
        cacheWeatherForecastInfo(addressDto.getAdCode(), weather);
        return ResponseEntity.ok(ApiResponse.success(weather));
    }

    private AddressDto getUserAddressInfoFromCache(Long userId) throws JsonProcessingException {
        String addressStr = redisTemplate.opsForValue().get(userId.toString());
        return objectMapper.readValue(addressStr, AddressDto.class);
    }

    private WeatherInfoDto readWeatherForecastInfoFromCache(String adCode) {
        String weatherStr = redisTemplate.opsForValue().get(getWeatherForecastInfoKeyForCache(adCode));
        if (!StringUtils.hasText(weatherStr)) {
            return null;
        }

        try {
            return objectMapper.readValue(weatherStr, WeatherInfoDto.class);
        } catch (JsonProcessingException e) {
            return null;
        }
    }

    private void cacheWeatherForecastInfo(String adCode, WeatherInfoDto weather) throws JsonProcessingException {
        String weatherStr = objectMapper.writeValueAsString(weather);
        redisTemplate.opsForValue().set(getWeatherForecastInfoKeyForCache(adCode), weatherStr, 60 * 60);
    }

    private String getWeatherForecastInfoKeyForCache(String adCode) {
        return adCode + "Forecast";
    }


    @GetMapping("/weather/by-ip")
    public ResponseEntity<ApiResponse<WeatherInfoDto>> weatherByIp() throws JsonProcessingException {
        IPInfo ipInfo = aMapService.getAddressByIp();
        if (ipInfo == null || !StringUtils.hasText(ipInfo.getAdcode())) {
            String defaultAdCode = "110000"; // Default to Beijing
            assert ipInfo != null;
            ipInfo.setAdcode(defaultAdCode);
        }

        WeatherInfoDto weather = readWeatherInfoFromCache(ipInfo.getAdcode());
        if (weather == null) {
            weather = aMapService.getWeather(ipInfo.getAdcode(), false);
            cacheWeatherInfo(ipInfo.getAdcode(), weather);
        }

        return ResponseEntity.ok(ApiResponse.success(weather));
    }

    private WeatherInfoDto readWeatherInfoFromCache(String adCode) {
        String weatherStr = redisTemplate.opsForValue().get(getWeatherInfoKeyForCache(adCode));
        if (!StringUtils.hasText(weatherStr)) {
            return null;
        }

        try {
            return objectMapper.readValue(weatherStr, WeatherInfoDto.class);
        } catch (JsonProcessingException e) {
            return null;
        }
    }

    private void cacheWeatherInfo(String adCode, WeatherInfoDto weather) throws JsonProcessingException {
        String weatherStr = objectMapper.writeValueAsString(weather);
        redisTemplate.opsForValue().set(getWeatherInfoKeyForCache(adCode), weatherStr, 60 * 60);
    }

    private String getWeatherInfoKeyForCache(String adCode) {
        return adCode + "Weather";
    }

}
