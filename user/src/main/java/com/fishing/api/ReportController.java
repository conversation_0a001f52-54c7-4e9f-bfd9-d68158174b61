package com.fishing.api;

import com.fishing.config.CurrentUser;
import com.fishing.dto.report.ReportMomentDTO;
import com.fishing.dto.report.ReportUserDTO;
import com.fishing.service.ReportService;
import com.fishing.util.JwtTokenUtil;
import com.fishing.vo.ApiResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 举报管理控制器
 */
@Tag(name = "举报管理", description = "举报相关接口")
@RestController
@RequestMapping("/reports")
@RequiredArgsConstructor
public class ReportController {

    private final ReportService reportService;
    private final JwtTokenUtil jwtTokenUtil;

    @PostMapping("/moment")
    @Operation(summary = "举报动态")
    public ResponseEntity<ApiResponse<Void>> reportMoment(
            @RequestBody @Valid ReportMomentDTO reportDTO,
            @CurrentUser Long userId) {
        reportService.reportMoment(userId, reportDTO);
        return ResponseEntity.ok(ApiResponse.success(null));
    }

    @PostMapping("/user")
    @Operation(summary = "举报用户")
    public ResponseEntity<ApiResponse<Void>> reportUser(
            @RequestBody @Valid ReportUserDTO reportDTO,
            @CurrentUser Long userId) {
        reportService.reportUser(userId, reportDTO);
        return ResponseEntity.ok(ApiResponse.success(null));
    }

    @GetMapping("/reasons")
    @Operation(summary = "获取举报原因列表")
    public ResponseEntity<ApiResponse<Map<String, List<String>>>> getReportReasons() {
        List<String> reasons = Arrays.asList(
                "垃圾信息",
                "违法违规",
                "色情内容",
                "暴力内容",
                "虚假信息",
                "侵犯版权",
                "其他"
        );
        return ResponseEntity.ok(ApiResponse.success(Map.of("reasons", reasons)));
    }
}
