package com.fishing.api;

import com.fishing.config.CurrentUser;
import com.fishing.service.BookmarkService;
import com.fishing.vo.ApiResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * 收藏管理控制器
 */
@Tag(name = "收藏管理", description = "收藏相关接口")
@RestController
@RequestMapping("/bookmarks")
@RequiredArgsConstructor
public class BookmarkController {

    private final BookmarkService bookmarkService;

    @PostMapping("/moment/{momentId}")
    @Operation(summary = "收藏动态")
    public ResponseEntity<ApiResponse<Void>> bookmarkMoment(@PathVariable Long momentId, @CurrentUser Long userId) {
        bookmarkService.bookmarkMoment(userId, momentId);
        return ResponseEntity.ok(ApiResponse.success(null));
    }

    @DeleteMapping("/moment/{momentId}")
    @Operation(summary = "取消收藏动态")
    public ResponseEntity<ApiResponse<Void>> unbookmarkMoment(@PathVariable Long momentId, @CurrentUser Long userId) {
        bookmarkService.unbookmarkMoment(userId, momentId);
        return ResponseEntity.ok(ApiResponse.success(null));
    }

    @GetMapping("/moment/{momentId}/status")
    @Operation(summary = "检查动态是否已收藏")
    public ResponseEntity<ApiResponse<Boolean>> isBookmarked(@PathVariable Long momentId, @CurrentUser Long userId) {
        boolean bookmarked = bookmarkService.isBookmarked(userId, momentId);
        return ResponseEntity.ok(ApiResponse.success(bookmarked));
    }
}
