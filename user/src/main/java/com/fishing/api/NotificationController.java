package com.fishing.api;

import com.fishing.config.CurrentUser;
import com.fishing.service.NotificationService;
import com.fishing.util.JwtTokenUtil;
import com.fishing.vo.ApiResponse;
import com.fishing.vo.notification.NotificationVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 通知管理控制器
 */
@Tag(name = "通知管理", description = "通知相关接口")
@RestController
@RequestMapping("/notifications")
@RequiredArgsConstructor
public class NotificationController {

    private final NotificationService notificationService;
    private final JwtTokenUtil jwtTokenUtil;

    @GetMapping
    @Operation(summary = "获取用户通知列表")
    public ResponseEntity<ApiResponse<List<NotificationVO>>> getNotifications(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) Boolean unreadOnly,
            @CurrentUser Long userId) {
        List<NotificationVO> notifications = notificationService.getUserNotifications(
                userId, page, size, unreadOnly);
        return ResponseEntity.ok(ApiResponse.success(notifications));
    }

    @PutMapping("/{notificationId}/read")
    @Operation(summary = "标记通知为已读")
    public ResponseEntity<ApiResponse<Void>> markAsRead(
            @PathVariable Long notificationId,
            @CurrentUser Long userId) {
        notificationService.markAsRead(userId, notificationId);
        return ResponseEntity.ok(ApiResponse.success(null));
    }

    @PutMapping("/read-all")
    @Operation(summary = "标记所有通知为已读")
    public ResponseEntity<ApiResponse<Void>> markAllAsRead(@CurrentUser Long userId) {
        notificationService.markAllAsRead(userId);
        return ResponseEntity.ok(ApiResponse.success(null));
    }

    @GetMapping("/unread-count")
    @Operation(summary = "获取未读通知数量")
    public ResponseEntity<ApiResponse<Map<String, Integer>>> getUnreadCount(
            @CurrentUser Long userId) {
        int count = notificationService.getUnreadCount(userId);
        return ResponseEntity.ok(ApiResponse.success(Map.of("count", count)));
    }

    @DeleteMapping("/{notificationId}")
    @Operation(summary = "删除通知")
    public ResponseEntity<ApiResponse<Void>> deleteNotification(
            @PathVariable Long notificationId,
            @CurrentUser Long userId) {
        notificationService.deleteNotification(userId, notificationId);
        return ResponseEntity.ok(ApiResponse.success(null));
    }
}
