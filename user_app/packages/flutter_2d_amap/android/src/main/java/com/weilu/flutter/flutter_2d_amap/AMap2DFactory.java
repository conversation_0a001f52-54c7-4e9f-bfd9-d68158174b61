package com.weilu.flutter.flutter_2d_amap;

import android.content.Context;
import android.graphics.Color;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.view.View;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.amap.api.location.AMapLocation;
import com.amap.api.location.AMapLocationClient;
import com.amap.api.location.AMapLocationClientOption;
import com.amap.api.location.AMapLocationListener;
import com.amap.api.maps2d.AMap;
import com.amap.api.maps2d.CameraUpdateFactory;
import com.amap.api.maps2d.LocationSource;
import com.amap.api.maps2d.MapView;
import com.amap.api.maps2d.model.BitmapDescriptorFactory;
import com.amap.api.maps2d.model.LatLng;
import com.amap.api.maps2d.model.LatLngBounds;
import com.amap.api.maps2d.model.Marker;
import com.amap.api.maps2d.model.MarkerOptions;
import com.amap.api.maps2d.model.MyLocationStyle;
import com.amap.api.services.core.AMapException;
import com.amap.api.services.core.LatLonPoint;
import com.amap.api.services.core.PoiItemV2;
import com.amap.api.services.geocoder.GeocodeResult;
import com.amap.api.services.geocoder.GeocodeSearch;
import com.amap.api.services.geocoder.RegeocodeAddress;
import com.amap.api.services.geocoder.RegeocodeQuery;
import com.amap.api.services.geocoder.RegeocodeResult;
import com.amap.api.services.poisearch.PoiResultV2;
import com.amap.api.services.poisearch.PoiSearchV2;
import com.google.gson.Gson;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import io.flutter.plugin.common.BinaryMessenger;
import io.flutter.plugin.common.MethodCall;
import io.flutter.plugin.common.MethodChannel;
import io.flutter.plugin.platform.PlatformView;

/**
 * <AUTHOR>
 * Refactored for robustness and feature completeness.
 */
public class AMap2DView implements PlatformView, MethodChannel.MethodCallHandler, LocationSource, AMapLocationListener,
        AMap.OnMapClickListener, PoiSearchV2.OnPoiSearchListener, GeocodeSearch.OnGeocodeSearchListener {

    private static final String SEARCH_CONTENT = "010000|010100|020000|030000|040000|050000|050100|060000|060100|060200|060300|060400|070000|080000|080100|080300|080500|080600|090000|090100|090200|090300|100000|100100|110000|110100|120000|120200|120300|130000|140000|141200|150000|150100|150200|160000|160100|170000|170100|170200|180000|190000|200000";
    private static final String IS_POI_SEARCH = "isPoiSearch";

    private final MethodChannel methodChannel;
    private final Context context;
    private final MapView mAMap2DView;
    private final AMap aMap;
    private final Handler platformThreadHandler = new Handler(Looper.getMainLooper());
    private final List<Marker> managedMarkers = new ArrayList<>(); // [MODIFIED] To manage multiple markers
    private boolean isPoiSearch;
    // Location and Search
    private OnLocationChangedListener mListener;
    private AMapLocationClient mLocationClient;
    private PoiSearchV2.Query query;
    private GeocodeSearch geocodeSearch;
    // State Management for Markers
    private Marker mCentralMarker; // For the single marker at the center

    AMap2DView(final Context context, BinaryMessenger messenger, int id, Map<String, Object> params, AMap2DDelegate delegate) {
        this.context = context;

        mAMap2DView = new MapView(context);
        mAMap2DView.onCreate(new Bundle());
        aMap = mAMap2DView.getMap();

        methodChannel = new MethodChannel(messenger, "plugins.weilu/flutter_2d_amap_" + id);
        methodChannel.setMethodCallHandler(this);

        if (params != null && params.containsKey(IS_POI_SEARCH) && params.get(IS_POI_SEARCH) instanceof Boolean) {
            isPoiSearch = (boolean) params.get(IS_POI_SEARCH);
        }

        if (delegate != null) {
            delegate.requestPermissions(new AMap2DDelegate.RequestPermission() {
                @Override
                public void onRequestPermissionSuccess() {
                    setUpMap();
                }

                @Override
                public void onRequestPermissionFailure() {
                    Toast.makeText(context, "定位失败，请检查定位权限是否开启！", Toast.LENGTH_SHORT).show();
                }
            });
        }
        mAMap2DView.onResume();
    }

    private void setUpMap() {
        aMap.setLocationSource(this);
        aMap.getUiSettings().setMyLocationButtonEnabled(true);
        aMap.setMyLocationEnabled(true);
        aMap.setOnMapClickListener(this);

        MyLocationStyle myLocationStyle = new MyLocationStyle();
        myLocationStyle.myLocationType(MyLocationStyle.LOCATION_TYPE_LOCATE);
        myLocationStyle.strokeColor(Color.parseColor("#8052A3FF"));
        myLocationStyle.radiusFillColor(Color.parseColor("#3052A3FF"));
        aMap.setMyLocationStyle(myLocationStyle);
    }

    @Override
    public void onMethodCall(@NonNull MethodCall call, @NonNull MethodChannel.Result result) {
        switch (call.method) {
            case "search":
                handleSearch(call, result);
                break;
            case "move":
                handleMove(call, result);
                break;
            case "location":
                if (mLocationClient != null) {
                    mLocationClient.startLocation();
                }
                result.success(null);
                break;
            case "reGeocode":
                handleReGeocode(call, result);
                break;
            case "setZoom":
                handleSetZoom(call, result);
                break;
            case "addMarkers":
                handleAddMarkers(call, result);
                break;
            // [IMPLEMENTED]
            case "clearMarkers":
                handleClearMarkers(result);
                break;
            // [IMPLEMENTED]
            case "zoomToFitMarkers":
                handleZoomToFitMarkers(result);
                break;
            default:
                result.notImplemented();
                break;
        }
    }

    // --- Method Handlers ---

    private void handleSearch(MethodCall call, MethodChannel.Result result) {
        String keyWord = call.argument("keyWord");
        String city = call.argument("city");
        search(keyWord, city);
        result.success(null);
    }

    private void handleMove(MethodCall call, MethodChannel.Result result) {
        Double lat = toDouble(call.argument("lat"));
        Double lon = toDouble(call.argument("lon"));
        if (lat != null && lon != null) {
            move(lat, lon);
        }
        result.success(null);
    }

    private void handleReGeocode(MethodCall call, MethodChannel.Result result) {
        Double lat = toDouble(call.argument("lat"));
        Double lon = toDouble(call.argument("lon"));
        if (lat == null || lon == null) {
            result.error("INVALID_ARGUMENT", "Latitude or Longitude is null", null);
            return;
        }
        try {
            if (geocodeSearch == null) {
                geocodeSearch = new GeocodeSearch(context);
                geocodeSearch.setOnGeocodeSearchListener(this);
            }
            LatLonPoint latLonPoint = new LatLonPoint(lat, lon);
            geocodeSearch.getFromLocationAsyn(new RegeocodeQuery(latLonPoint, 200, GeocodeSearch.AMAP));
            result.success(null);
        } catch (AMapException e) {
            result.error("GEOCODE_ERROR", e.getErrorMessage(), null);
        }
    }

    private void handleSetZoom(MethodCall call, MethodChannel.Result result) {
        Float zoom = toFloat(call.argument("zoomLevel"));
        if (zoom != null) {
            aMap.moveCamera(CameraUpdateFactory.zoomTo(zoom));
        }
        result.success(null);
    }

    private void handleAddMarkers(MethodCall call, MethodChannel.Result result) {
        List<Map<String, Object>> markers = call.argument("markers");
        if (markers == null || markers.isEmpty()) {
            result.error("INVALID_ARGUMENTS", "markers cannot be null or empty", null);
            return;
        }
        try {
            for (Map<String, Object> markerData : markers) {
                Double latitude = toDouble(markerData.get("latitude"));
                Double longitude = toDouble(markerData.get("longitude"));

                if (latitude != null && longitude != null) {
                    MarkerOptions markerOptions = new MarkerOptions()
                            .position(new LatLng(latitude, longitude));
                    // [MODIFIED] Save reference to the added marker
                    Marker marker = aMap.addMarker(markerOptions);
                    managedMarkers.add(marker);
                }
            }
            result.success(null);
        } catch (Exception e) {
            result.error("MARKERS_ADD_FAILED", e.getMessage(), null);
        }
    }

    private void handleClearMarkers(MethodChannel.Result result) {
        for (Marker marker : managedMarkers) {
            marker.remove();
        }
        managedMarkers.clear();
        result.success(null);
    }

    private void handleZoomToFitMarkers(MethodChannel.Result result) {
        if (managedMarkers.isEmpty()) {
            result.success(null);
            return;
        }
        LatLngBounds.Builder boundsBuilder = new LatLngBounds.Builder();
        for (Marker marker : managedMarkers) {
            boundsBuilder.include(marker.getPosition());
        }
        LatLngBounds bounds = boundsBuilder.build();
        // The second argument is padding in pixels
        aMap.animateCamera(CameraUpdateFactory.newLatLngBounds(bounds, 100));
        result.success(null);
    }

    // --- AMap Listeners ---

    @Override
    public void onLocationChanged(AMapLocation aMapLocation) {
        if (mListener != null && aMapLocation != null) {
            if (aMapLocation.getErrorCode() == 0) {
                mListener.onLocationChanged(aMapLocation);
                searchAround(aMapLocation.getLatitude(), aMapLocation.getLongitude());

                invokeMainThread("onLocationChanged", new HashMap<String, Object>() {{
                    put("latitude", String.valueOf(aMapLocation.getLatitude()));
                    put("longitude", String.valueOf(aMapLocation.getLongitude()));
                }});
            } else {
                Toast.makeText(context, "定位失败：" + aMapLocation.getErrorInfo(), Toast.LENGTH_SHORT).show();
            }
        }
        if (mLocationClient != null) {
            mLocationClient.stopLocation();
        }
    }

    @Override
    public void onMapClick(LatLng latLng) {
        drawCentralMarker(latLng);
        searchAround(latLng.latitude, latLng.longitude);
    }

    @Override
    public void onPoiSearched(PoiResultV2 result, int code) {
        List<Map<String, String>> poiDataList = new ArrayList<>();
        if (code == AMapException.CODE_AMAP_SUCCESS && result != null && result.getPois() != null) {
            for (PoiItemV2 item : result.getPois()) {
                poiDataList.add(poiItemToMap(item));
            }
            if (!result.getPois().isEmpty()) {
                PoiItemV2 firstPoi = result.getPois().get(0);
                move(firstPoi.getLatLonPoint().getLatitude(), firstPoi.getLatLonPoint().getLongitude());
            }
        }
        invokeMainThread("poiSearchResult", new HashMap<String, Object>() {{
            put("poiSearchResult", new Gson().toJson(poiDataList));
        }});
    }

    @Override
    public void onRegeocodeSearched(RegeocodeResult regeocodeResult, int code) {
        // [CRITICAL FIX] Handle both success and failure cases safely
        final Map<String, Object> map = new HashMap<>();
        if (code == AMapException.CODE_AMAP_SUCCESS && regeocodeResult != null && regeocodeResult.getRegeocodeAddress() != null) {
            RegeocodeAddress address = regeocodeResult.getRegeocodeAddress();
            map.put("status", "1");
            map.put("info", "SUCCESS");
            map.put("province", safeString(address.getProvince()));
            map.put("city", safeString(address.getCity()));
            map.put("district", safeString(address.getDistrict()));
            map.put("township", safeString(address.getTownship()));
            map.put("formatAddress", safeString(address.getFormatAddress()));
            if (address.getStreetNumber() != null) {
                map.put("street", safeString(address.getStreetNumber().getStreet()));
                map.put("streetNumber", safeString(address.getStreetNumber().getNumber()));
            } else {
                map.put("street", "");
                map.put("streetNumber", "");
            }
        } else {
            map.put("status", "0");
            map.put("info", "RE GEOCODE FAILED");
            map.put("formatAddress", "地址解析失败");
            map.put("province", "");
            map.put("city", "");
            map.put("district", "");
            map.put("township", "");
            map.put("street", "");
            map.put("streetNumber", "");
        }
        invokeMainThread("onReGeocode", map);
    }

    @Override
    public void onGeocodeSearched(GeocodeResult geocodeResult, int i) {
        // Not used
    }

    // --- LocationSource implementation ---

    @Override
    public void activate(OnLocationChangedListener onLocationChangedListener) {
        mListener = onLocationChangedListener;
        if (mLocationClient == null) {
            try {
                mLocationClient = new AMapLocationClient(context);
                mLocationClient.setLocationListener(this);
                AMapLocationClientOption locationOption = new AMapLocationClientOption();
                locationOption.setLocationMode(AMapLocationClientOption.AMapLocationMode.Hight_Accuracy);
                mLocationClient.setLocationOption(locationOption);
                mLocationClient.startLocation();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    @Override
    public void deactivate() {
        mListener = null;
        if (mLocationClient != null) {
            mLocationClient.stopLocation();
            mLocationClient.onDestroy();
        }
        mLocationClient = null;
    }

    // --- Helper Methods ---

    private void search(String keyWord, String city) {
        if (!isPoiSearch) return;
        query = new PoiSearchV2.Query(keyWord, SEARCH_CONTENT, city);
        query.setPageSize(50);
        query.setPageNum(0);
        try {
            PoiSearchV2 poiSearch = new PoiSearchV2(context, query);
            poiSearch.setOnPoiSearchListener(this);
            poiSearch.searchPOIAsyn();
        } catch (AMapException e) {
            e.printStackTrace();
        }
    }

    private void searchAround(double latitude, double longitude) {
        if (!isPoiSearch) return;
        query = new PoiSearchV2.Query("", SEARCH_CONTENT, "");
        query.setPageSize(50);
        query.setPageNum(0);
        try {
            PoiSearchV2 poiSearch = new PoiSearchV2(context, query);
            poiSearch.setOnPoiSearchListener(this);
            poiSearch.setBound(new PoiSearchV2.SearchBound(new LatLonPoint(latitude, longitude), 2000, true));
            poiSearch.searchPOIAsyn();
        } catch (AMapException e) {
            e.printStackTrace();
        }
    }

    private void move(double lat, double lon) {
        LatLng latLng = new LatLng(lat, lon);
        aMap.animateCamera(CameraUpdateFactory.newLatLngZoom(latLng, 16));
        drawCentralMarker(latLng);
    }

    private void drawCentralMarker(LatLng latLng) {
        if (mCentralMarker == null) {
            mCentralMarker = aMap.addMarker(new MarkerOptions().position(latLng));
        } else {
            mCentralMarker.setPosition(latLng);
        }
    }

    private Map<String, String> poiItemToMap(PoiItemV2 item) {
        Map<String, String> poiData = new LinkedHashMap<>();
        if (item == null) return poiData;
        poiData.put("cityCode", safeString(item.getCityCode()));
        poiData.put("cityName", safeString(item.getCityName()));
        poiData.put("provinceName", safeString(item.getProvinceName()));
        poiData.put("title", safeString(item.getTitle()));
        poiData.put("adName", safeString(item.getAdName()));
        poiData.put("provinceCode", safeString(item.getProvinceCode()));
        if (item.getLatLonPoint() != null) {
            poiData.put("latitude", String.valueOf(item.getLatLonPoint().getLatitude()));
            poiData.put("longitude", String.valueOf(item.getLatLonPoint().getLongitude()));
        } else {
            poiData.put("latitude", "0.0");
            poiData.put("longitude", "0.0");
        }
        return poiData;
    }

    private void invokeMainThread(final String method, final Map<String, Object> arguments) {
        platformThreadHandler.post(() -> methodChannel.invokeMethod(method, arguments));
    }

    // --- Safe Casting/Parsing Helpers ---

    private Double toDouble(Object o) {
        if (o == null) return null;
        try {
            return Double.parseDouble(o.toString());
        } catch (NumberFormatException e) {
            return null;
        }
    }

    private Float toFloat(Object o) {
        if (o == null) return null;
        try {
            return Float.parseFloat(o.toString());
        } catch (NumberFormatException e) {
            return null;
        }
    }

    private String safeString(String s) {
        return s == null ? "" : s;
    }

    // --- PlatformView Lifecycle ---
    @Override
    public View getView() {
        return mAMap2DView;
    }

    @Override
    public void dispose() {
        if (mAMap2DView != null) {
            mAMap2DView.onDestroy();
        }
        if (mLocationClient != null) {
            mLocationClient.onDestroy();
        }
        platformThreadHandler.removeCallbacksAndMessages(null);
        methodChannel.setMethodCallHandler(null);
    }

    // Not used
    @Override
    public void onPoiItemSearched(PoiItemV2 poiItemV2, int i) {
    }
}