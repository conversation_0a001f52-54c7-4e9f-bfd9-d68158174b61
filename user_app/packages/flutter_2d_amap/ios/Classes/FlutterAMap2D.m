#import "FlutterAMap2D.h"
#import <MAMapKit/MAMapKit.h>
#import <AMapFoundationKit/AMapFoundationKit.h>
#import <CoreLocation/CoreLocation.h>
#import <AMapLocationKit/AMapLocationKit.h>
#import <AMapSearchKit/AMapSearchKit.h>
#import <CoreGraphics/CoreGraphics.h>

@interface FlutterAMap2DController()<AMapLocationManagerDelegate, AMapSearchDelegate, CLLocationManagerDelegate, MAMapViewDelegate>

@property (strong, nonatomic) CLLocationManager *manager;
@property (strong, nonatomic) AMapLocationManager *locationManager;
@property (strong, nonatomic) AMapSearchAPI *search;

// [MODIFIED] - 添加一个数组来管理通过 addMarkers 添加的标注
@property (strong, nonatomic) NSMutableArray<MAPointAnnotation *> *managedAnnotations;

@end

@implementation FlutterAMap2DController {
    MAMapView* _mapView;
    int64_t _viewId;
    FlutterMethodChannel* _channel;

    MAPointAnnotation* _pointAnnotation; // 用于表示地图中心的单点
    bool _isPoiSearch;
}

NSString* _types = @"010000|010100|020000|030000|040000|050000|050100|060000|060100|060200|060300|060400|070000|080000|080100|080300|080500|080600|090000|090100|090200|090300|100000|100100|110000|110100|120000|120200|120300|130000|140000|141200|150000|150100|150200|160000|160100|170000|170100|170200|180000|190000|200000";

- (instancetype)initWithFrame:(CGRect)frame
               viewIdentifier:(int64_t)viewId
                    arguments:(id _Nullable)args
              binaryMessenger:(NSObject<FlutterBinaryMessenger>*)messenger {
    if ([super init]) {
        _viewId = viewId;
        NSString* channelName = [NSString stringWithFormat:@"plugins.weilu/flutter_2d_amap_%lld", viewId];
        _channel = [FlutterMethodChannel methodChannelWithName:channelName binaryMessenger:messenger];
        __weak __typeof__(self) weakSelf = self;
        [_channel setMethodCallHandler:^(FlutterMethodCall* call, FlutterResult result) {
            [weakSelf onMethodCall:call result:result];
        }];
        _isPoiSearch = [args[@"isPoiSearch"] boolValue] == YES;

        // [MODIFIED] - 初始化标注管理数组
        _managedAnnotations = [NSMutableArray array];

        /// 初始化地图
        _mapView = [[MAMapView alloc] initWithFrame:frame];
        _mapView.delegate = self;

        // 请求定位权限
        self.manager =  [[CLLocationManager alloc] init];
        self.manager.delegate = self;
        [self.manager requestWhenInUseAuthorization];
    }
    return self;
}

-(void)locationManager:(CLLocationManager *)manager didChangeAuthorizationStatus:(CLAuthorizationStatus)status {
    switch (status) {
        case kCLAuthorizationStatusAuthorizedAlways:
        case kCLAuthorizationStatusAuthorizedWhenInUse:{
            _mapView.showsUserLocation = YES;
            _mapView.userTrackingMode = MAUserTrackingModeFollow;

            /// 初始化定位
            self.locationManager = [[AMapLocationManager alloc] init];
            self.locationManager.delegate = self;
            [_mapView setMinZoomLevel:3.0];
            [_mapView setMaxZoomLevel:19.0];

            /// 开始定位
            [self.locationManager startUpdatingLocation];
            /// 初始化搜索
            self.search = [[AMapSearchAPI alloc] init];
            self.search.delegate = self;
            break;
        }
        default:
            NSLog(@"授权失败");
            break;
    }
}

#pragma mark - Map Delegate Methods

- (void)mapView:(MAMapView *)mapView didSingleTappedAtCoordinate:(CLLocationCoordinate2D)coordinate {
    [self->_mapView setCenterCoordinate:coordinate animated:YES];
    [self drawCentralMarkerAtLat:coordinate.latitude lon:coordinate.longitude];
    [self searchPOIAtLat:coordinate.latitude lon:coordinate.longitude];
}

#pragma mark - Location Delegate Methods

- (void)amapLocationManager:(AMapLocationManager *)manager didUpdateLocation:(CLLocation *)location reGeocode:(AMapLocationReGeocode *)reGeocode{
    [self.locationManager stopUpdatingLocation];
    [self searchPOIAtLat:location.coordinate.latitude lon:location.coordinate.longitude];

    NSDictionary* arguments = @{
            @"latitude" : @(location.coordinate.latitude),
            @"longitude" : @(location.coordinate.longitude),
    };
    [_channel invokeMethod:@"onLocationChanged" arguments:arguments];
}

#pragma mark - Search Delegate Methods

/* POI 搜索回调. */
- (void)onPOISearchDone:(AMapPOISearchBaseRequest *)request response:(AMapPOISearchResponse *)response{
    if (!response || response.pois.count == 0) {
        [self handleSearchError:@"No POI data found"];
        return;
    }

    NSMutableArray *poisArray = [NSMutableArray array];
    [response.pois enumerateObjectsUsingBlock:^(AMapPOI *obj, NSUInteger idx, BOOL *stop) {
        // [ROBUSTNESS] - 使用 nil-coalescing 确保所有值都非 nil
        NSDictionary *poiDict = @{
                @"cityCode": obj.citycode ?: @"",
                @"cityName": obj.city ?: @"",
                @"provinceName": obj.province ?: @"",
                @"title": obj.name ?: @"",
                @"adName": obj.district ?: @"",
                @"provinceCode": obj.pcode ?: @"",
                @"latitude": [NSString stringWithFormat:@"%f", obj.location.latitude],
                @"longitude": [NSString stringWithFormat:@"%f", obj.location.longitude],
                @"address": obj.address ?: @"",
        };
        [poisArray addObject:poiDict];
    }];

    NSError *error;
    NSData *jsonData = [NSJSONSerialization dataWithJSONObject:poisArray options:0 error:&error];
    if (error) {
        [self handleJSONError:error];
        return;
    }

    NSString *jsonString = [[NSString alloc] initWithData:jsonData encoding:NSUTF8StringEncoding];
    NSDictionary* arguments = @{@"poiSearchResult": jsonString ?: @"[]"};
    [_channel invokeMethod:@"poiSearchResult" arguments:arguments];
}

/* 逆地理编码回调 */
- (void)onReGeocodeSearchDone:(AMapReGeocodeSearchRequest *)request response:(AMapReGeocodeSearchResponse *)response {
    // [CRITICAL FIX] - 完整处理成功和失败的情况
    if (response && response.regeocode) {
        AMapAddressComponent *addressComponent = response.regeocode.addressComponent;

        // [CRITICAL FIX] - 构建字典前检查所有值是否为 nil，防止崩溃
        NSDictionary* arguments = @{
                @"status": @"1",
                @"info": @"SUCCESS",
                @"province" : addressComponent.province ?: @"",
                @"city" : addressComponent.city ?: @"",
                @"district" : addressComponent.district ?: @"",
                @"township" : addressComponent.township ?: @"",
                @"street" : addressComponent.streetNumber.street ?: @"",
                @"streetNumber" : addressComponent.streetNumber.number ?: @"",
                @"formatAddress" : response.regeocode.formattedAddress ?: @"",
        };
        [_channel invokeMethod:@"onReGeocode" arguments:arguments];
    } else {
        // [ROBUSTNESS] - 当逆地理编码失败时，通知 Dart
        NSDictionary* arguments = @{
                @"status": @"0",
                @"info": @"RE GEOCODE FAILED",
                @"province" : @"",
                @"city" : @"",
                @"district" : @"",
                @"township" : @"",
                @"street" : @"",
                @"streetNumber" : @"",
                @"formatAddress" : @"地址解析失败",
        };
        [_channel invokeMethod:@"onReGeocode" arguments:arguments];
    }
}

/* 搜索失败回调. */
- (void)AMapSearchRequest:(id)request didFailWithError:(NSError *)error {
    NSLog(@"Search request failed with error: %@", error);
    // 可选：在这里向 Dart 发送一个通用的错误通知
}

#pragma mark - Helper Methods

- (void)handleSearchError:(NSString *)errorMessage {
    NSLog(@"POI search error: %@", errorMessage);
    NSDictionary* arguments = @{@"poiSearchResult": @"[]"};
    [_channel invokeMethod:@"poiSearchResult" arguments:arguments];
}

- (void)handleJSONError:(NSError *)error {
    NSLog(@"JSON serialization error: %@", error);
    NSDictionary* arguments = @{@"poiSearchResult": @"[]"};
    [_channel invokeMethod:@"poiSearchResult" arguments:arguments];
}

- (UIView*)view {
    return _mapView;
}

// 绘制并管理地图中心的单个标记
- (void)drawCentralMarkerAtLat:(CGFloat)lat lon:(CGFloat)lon {
    if (self->_pointAnnotation == NULL) {
        self->_pointAnnotation = [[MAPointAnnotation alloc] init];
        [self->_mapView addAnnotation:self->_pointAnnotation];
    }
    self->_pointAnnotation.coordinate = CLLocationCoordinate2DMake(lat, lon);
}

// 搜索指定经纬度的POI
- (void)searchPOIAtLat:(CGFloat)lat lon:(CGFloat)lon {
    if (_isPoiSearch) {
        AMapPOIAroundSearchRequest *request = [[AMapPOIAroundSearchRequest alloc] init];
        request.types = _types;
        request.requireExtension = YES;
        request.offset = 50;
        request.location = [AMapGeoPoint locationWithLatitude:lat longitude:lon];
        [self.search AMapPOIAroundSearch:request];
    }
}

#pragma mark - Method Channel Handler

- (void)onMethodCall:(FlutterMethodCall*)call result:(FlutterResult)result {
    if ([call.method isEqualToString:@"search"]) {
        if (_isPoiSearch) {
            AMapPOIKeywordsSearchRequest *request = [[AMapPOIKeywordsSearchRequest alloc] init];
            request.types = _types;
            request.requireExtension = YES;
            request.offset = 50;
            request.keywords = [call arguments][@"keyWord"];
            request.city = [call arguments][@"city"];
            [self.search AMapPOIKeywordsSearch:request];
        }
        result(nil);
    } else if ([call.method isEqualToString:@"move"]) {
        NSString* lat = [call arguments][@"lat"];
        NSString* lon = [call arguments][@"lon"];
        CLLocationCoordinate2D center = CLLocationCoordinate2DMake([lat doubleValue], [lon doubleValue]);
        [self->_mapView setCenterCoordinate:center animated:YES];
        [self drawCentralMarkerAtLat:[lat doubleValue] lon:[lon doubleValue]];
        result(nil);
    } else if ([call.method isEqualToString:@"location"]) {
        [self.locationManager startUpdatingLocation];
        result(nil);
    } else if ([call.method isEqualToString:@"reGeocode"]) {
        id latArg = [call arguments][@"lat"];
        id lonArg = [call arguments][@"lon"];
        AMapReGeocodeSearchRequest *regeo = [[AMapReGeocodeSearchRequest alloc] init];
        regeo.location = [AMapGeoPoint locationWithLatitude:[latArg doubleValue] longitude:[lonArg doubleValue]];
        regeo.requireExtension = YES;
        [self.search AMapReGoecodeSearch:regeo];
        result(nil);
    } else if ([call.method isEqualToString:@"setZoom"]) {
        CGFloat zoom = [[call arguments][@"zoomLevel"] floatValue];
        [_mapView setZoomLevel:zoom animated:YES];
        result(nil);
    } else if ([call.method isEqualToString:@"addMarkers"]) {
        // [MODIFIED] - 正确处理 addMarkers
        NSArray<NSDictionary *> *markers = [call arguments][@"markers"];
        for (NSDictionary *markerInfo in markers) {
            id latArg = markerInfo[@"latitude"];
            id lonArg = markerInfo[@"longitude"];
            if (latArg && lonArg) {
                MAPointAnnotation *pointAnnotation = [[MAPointAnnotation alloc] init];
                pointAnnotation.coordinate = CLLocationCoordinate2DMake([latArg doubleValue], [lonArg doubleValue]);
                [self.managedAnnotations addObject:pointAnnotation]; // 保存引用
                [self->_mapView addAnnotation:pointAnnotation];
            }
        }
        result(nil);
    } else if ([call.method isEqualToString:@"clearMarkers"]) {
        // [IMPLEMENTED] - 实现 clearMarkers
        if (self.managedAnnotations.count > 0) {
            [self->_mapView removeAnnotations:self.managedAnnotations];
            [self.managedAnnotations removeAllObjects];
        }
        result(nil);
    } else if ([call.method isEqualToString:@"zoomToFitMarkers"]) {
        // [IMPLEMENTED] - 实现 zoomToFitMarkers
        if (self.managedAnnotations.count > 0) {
            [self->_mapView showAnnotations:self.managedAnnotations animated:YES];
        }
        result(nil);
    } else {
        // [ROBUSTNESS] - 处理未实现的方法
        result(FlutterMethodNotImplemented);
    }
}

@end


@implementation FlutterAMap2DFactory {
    NSObject<FlutterBinaryMessenger>* _messenger;
}

- (instancetype)initWithMessenger:(NSObject<FlutterBinaryMessenger>*)messenger {
    self = [super init];
    if (self) {
        _messenger = messenger;
    }
    return self;
}

- (NSObject<FlutterMessageCodec>*)createArgsCodec {
    return [FlutterStandardMessageCodec sharedInstance];
}

- (NSObject<FlutterPlatformView>*)createWithFrame:(CGRect)frame
                                   viewIdentifier:(int64_t)viewId
                                        arguments:(id _Nullable)args {
    FlutterAMap2DController* aMap2DController = [[FlutterAMap2DController alloc] initWithFrame:frame
                                                                                viewIdentifier:viewId
                                                                                     arguments:args
                                                                               binaryMessenger:_messenger];
    return aMap2DController;
}

@end