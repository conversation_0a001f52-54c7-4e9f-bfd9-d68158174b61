import 'package:flutter_2d_amap/src/model/lng_lat.dart';

class DrivingResultModel {
  DrivingResultModel({
    required this.destination,
    required this.origin,
    required this.routes,
  });

  /// [MODIFIED] - 添加一个工厂构造函数来创建表示失败或空路径状态的实例。
  /// 当API调用失败时，可以返回这个对象。UI层只需检查 `routes` 列表是否为空。
  /// 接收起点和终点作为参数，以便UI仍然知道查询的上下文。
  factory DrivingResultModel.empty({
    required LngLat origin,
    required LngLat destination,
  }) {
    return DrivingResultModel(
      origin: origin,
      destination: destination,
      routes: [], // 空的路径列表表示没有找到路线
    );
  }

  final LngLat destination;
  final LngLat origin;
  final List<Route> routes;
}

class Route {
  Route({
    required this.distance,
    required this.duration,
    required this.steps,
  });

  final num distance;
  final num duration;
  final List<Step> steps;
}

class Step {
  Step({required this.path});

  final List<LngLat> path;
}
