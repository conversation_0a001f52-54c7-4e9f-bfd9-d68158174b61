import 'package:flutter_2d_amap/flutter_2d_amap.dart';

/// 逆地理编码结果
class ReGeocodeResult {
  ReGeocodeResult({
    required this.regeocode,
    required this.info,
    required this.status,
  });

  /// [MODIFIED] - 添加一个工厂构造函数来创建表示失败或空状态的实例。
  /// 这使得控制器在API调用失败时，可以返回一个非空的、定义清晰的“空”对象，
  /// 从而简化UI层的逻辑，避免处理null。
  factory ReGeocodeResult.empty() {
    return ReGeocodeResult(
      regeocode: ReGeocode(
        addressComponent: null, // 在失败情况下，地址组件为null是合理的
        formattedAddress: '未能获取地址信息', // 提供一个用户友好的默认失败消息
        pois: [],
        roads: [],
      ),
      info: 'FAILED', // 使用一个清晰的标识符表示失败
      status: '0', // 状态码'0'通常表示失败或无结果
    );
  }

  final ReGeocode regeocode;
  final String info;
  final String status;
}

/// 逆地理编码信息
class ReGeocode {
  ReGeocode({
    required this.addressComponent,
    required this.formattedAddress,
    required this.pois,
    required this.roads,
  });

  final AddressComponent? addressComponent;
  final String formattedAddress;
  final List<AMapPoi> pois;
  final List<AMapRoad> roads;
}

/// 地址组成要素
class AddressComponent {
  AddressComponent({
    required this.province,
    required this.city,
    required this.district,
    required this.township,
    required this.street,
    required this.streetNumber,
  });

  final String province;
  final String city;
  final String district;
  final String township;
  final String street;
  final String streetNumber;
}

/// 兴趣点 (Point of Interest)
class AMapPoi {
  AMapPoi({
    required this.id,
    required this.name,
    required this.type,
    required this.distance,
    required this.location,
    required this.address,
  });

  final String id;
  final String name;
  final String type;
  final double distance;
  final LngLat location;
  final String address;
}

/// 道路信息
class AMapRoad {
  AMapRoad({
    required this.id,
    required this.name,
    required this.distance,
    required this.direction,
    required this.location,
  });

  final String id;
  final String name;
  final double distance;
  final String direction;
  final LngLat location;
}
