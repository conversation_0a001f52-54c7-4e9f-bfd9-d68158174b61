import 'dart:convert';
import 'dart:developer';

import 'package:flutter/services.dart';
import 'package:flutter_2d_amap/flutter_2d_amap.dart';

class AMap2DMobileController extends AMap2DController {
  AMap2DMobileController(
    int id,
    this._widget,
  ) : _channel = MethodChannel('plugins.weilu/flutter_2d_amap_$id') {
    _channel.setMethodCallHandler(_handleMethod);
  }

  final MethodChannel _channel;
  final AMap2DView _widget;

  Future<dynamic> _handleMethod(MethodCall call) async {
    try {
      final String method = call.method;
      final args = call.arguments as Map<Object?, Object?>?;

      switch (method) {
        case 'poiSearchResult':
          {
            if (_widget.onPoiSearched != null && args != null) {
              final searchResultJson = args['poiSearchResult'] as String?;
              if (searchResultJson != null) {
                final List<PoiSearch> list =
                    (json.decode(searchResultJson) as List)
                        .map((item) =>
                            PoiSearch.fromJsonMap(item as Map<String, dynamic>))
                        .toList();
                _widget.onPoiSearched!(list);
              }
            }
            break;
          }
        case 'onLocationChanged':
          {
            if (_widget.onGetLocation != null && args != null) {
              final lon = double.tryParse(args['longitude']?.toString() ?? '');
              final lat = double.tryParse(args['latitude']?.toString() ?? '');

              if (lon != null && lat != null) {
                _widget.onGetLocation!(LngLat(lon, lat));
              }
            }
            break;
          }
        case 'onReGeocode':
          {
            if (_widget.onReGeocode != null && args != null) {
              final reGeocode = ReGeocode(
                addressComponent: AddressComponent(
                  province: args['province']?.toString() ?? '',
                  city: args['city']?.toString() ?? '',
                  district: args['district']?.toString() ?? '',
                  township: args['township']?.toString() ?? '',
                  street: args['street']?.toString() ?? '',
                  streetNumber: args['streetNumber']?.toString() ?? '',
                ),
                formattedAddress: args['formatAddress']?.toString() ?? '未能获取地址',
                pois: [],
                roads: [],
              );
              final reGeocodeResult = ReGeocodeResult(
                regeocode: reGeocode,
                info: args['info']?.toString() ?? 'SUCCESS',
                status: args['status']?.toString() ?? '1',
              );

              _widget.onReGeocode!(reGeocodeResult);
            }
            break;
          }
      }
    } catch (e, s) {
      log(
        'Error in _handleMethod: ${call.method}',
        error: e,
        stackTrace: s,
      );
    }
    return Future<dynamic>.value('');
  }

  Future<void> _invokeMethod(String method, [dynamic arguments]) async {
    try {
      await _channel.invokeMethod(method, arguments);
    } on PlatformException catch (e, s) {
      log(
        'PlatformException invoking $method',
        error: e,
        stackTrace: s,
      );
    } catch (e, s) {
      log(
        'Error invoking $method',
        error: e,
        stackTrace: s,
      );
    }
  }

  @override
  Future<void> search(String keyWord, {String city = ''}) async {
    return _invokeMethod('search', <String, dynamic>{
      'keyWord': keyWord,
      'city': city,
    });
  }

  @override
  Future<void> move(String lat, String lon) async {
    return _invokeMethod('move', <String, dynamic>{'lat': lat, 'lon': lon});
  }

  @override
  Future<void> location() async {
    return _invokeMethod('location');
  }

  @override
  Future<void> addMarkers(List<Map<String, String>> markers) {
    return _invokeMethod('addMarkers', <String, dynamic>{
      'markers': markers,
    });
  }

  @override
  Future<void> clearMarkers() {
    return _invokeMethod('clearMarkers');
  }

  @override
  Future<void> reGeocode(num lat, num lon) {
    return _invokeMethod('reGeocode', <String, dynamic>{
      'lat': lat,
      'lon': lon,
    });
  }

  @override
  Future<void> setZoom({num zoomLevel = 12}) async {
    return _invokeMethod('setZoom', <String, dynamic>{
      'zoomLevel': zoomLevel,
    });
  }

  @override
  Future<void> zoomToFitMarkers() {
    // [COMPLETED] - Native functionality for iOS and Android is now implemented.
    return _invokeMethod('zoomToFitMarkers');
  }

  @override
  Future<void> searchDrivingRoute(
      {required num fromLat,
      required num fromLon,
      required num toLat,
      required num toLon}) {
    // TODO: 在 iOS 和 Android 端实现 searchDrivingRoute 功能
    log('searchDrivingRoute is not implemented for mobile yet.');
    throw UnimplementedError(
        'searchDrivingRoute is not implemented for mobile');
  }

  @override
  Future<void> polyline(
      LngLat origin, LngLat destination, List<LngLat> points) {
    // TODO: 在 iOS 和 Android 端实现 polyline 功能
    log('polyline is not implemented for mobile yet.');
    throw UnimplementedError('polyline is not implemented for mobile');
  }
}
