// ignore_for_file: always_declare_return_types

@JS('AMap')
library amap;

import 'package:flutter_2d_amap/src/js_model/amap_lng_lat.dart';
import 'package:flutter_2d_amap/src/js_model/amap_regeocode_types.dart';
import 'package:js/js.dart';

/// 高德地图js，文档：https://lbs.amap.com/api/javascript-api/guide/abc/prepare
@JS('Map')
class AMap {
  external AMap(dynamic /*String|DivElement*/ div, MapOptions opts);

  /// 重新计算容器大小
  external resize();

  /// 设置中心点
  external setCenter(AmpJsLngLat center);

  /// 设置地图显示的缩放级别，参数 zoom 可设范围：[2, 20]
  external setZoom(num zoom);

  /// 添加覆盖物/图层。参数为单个覆盖物/图层，或覆盖物/图层的数组。
  external add(dynamic /*Array<any> | Marker*/ features);

  /// 删除覆盖物/图层。参数为单个覆盖物/图层，或覆盖物/图层的数组。
  external remove(dynamic /*Array | Marker*/ features);

  /// 删除所有覆盖物
  external clearMap();

  /// 加载插件
  external plugin(dynamic /*String|List*/ name, void Function() callback);

  /// 添加控件，参数可以是插件列表中的任何插件对象，如：ToolBar、OverView、Scale等
  external addControl(Control control);

  /// 销毁地图，并清空地图容器
  external destroy();

  external on(String eventName, void Function(MapsEvent event) callback);

  external getZoom();

  external setFitView([dynamic /*Array|Marker*/ markers]);
}

@JS()
class Geolocation extends Control {
  external Geolocation(GeolocationOptions opts);

  external getCurrentPosition(
      Function(String status, GeolocationResult result) callback);
}

@JS()
class Geocoder {
  external Geocoder(GeocoderOptions opts);

  external getAddress(AmpJsLngLat lnglat,
      Function(String status, JSReGeocodeResult result) callback);

  external getLocation(
      String address, Function(String status, dynamic result) callback);
}

@JS()
class PlaceSearch {
  external PlaceSearch(PlaceSearchOptions opts);

  external search(
      String keyword, Function(String status, SearchResult result) callback);

  /// 根据中心点经纬度、半径以及关键字进行周边查询 radius取值范围：0-50000
  external searchNearBy(String keyword, AmpJsLngLat center, num radius,
      Function(String status, SearchResult result) callback);

  external setType(String type);

  external setPageIndex(int pageIndex);

  external setPageSize(int pageSize);

  external setCity(String city);
}

@JS()
@anonymous
class DrivingOptions {
  external factory DrivingOptions({
    String policy,
    bool hideMarkers,
    bool showTraffic,
  });
}

@JS()
class Driving {
  external Driving(DrivingOptions opts);

  external search(AmpJsLngLat from, AmpJsLngLat to,
      Function(String status, DrivingResult result) callback);
}

@JS()
class DrivingResult {
  external String info;
  external AmpJsLngLat origin;
  external AmpJsLngLat destination;
  external List<DriveRoute> routes;
}

@JS()
class DriveRoute {
  external num distance;
  external num time;
  external List<DriveStep> steps;
}

@JS()
class DriveStep {
  external String instruction;
  external num distance;
  external num time;
  external List<AmpJsLngLat> path;
}

@JS()
class Polyline {
  external Polyline(PolylineOptions opts);

  external setPath(List<AmpJsLngLat> path);
}

@JS()
@anonymous
class PolylineOptions {
  external factory PolylineOptions({
    AMap map,
    List<AmpJsLngLat> path,
    num strokeWeight,
    String strokeColor,
    String outlineColor,
    bool isOutline,
    num borderWeight,
    String lineJoin,
  });

  external AMap get map;

  external List<AmpJsLngLat> get path;

  external num get strokeWeight;

  external String get strokeColor;

  external num get strokeOpacity;
}

@JS()
class Pixel {
  external Pixel(num x, num y);
}

@JS()
class Marker {
  external Marker(MarkerOptions opts);

  /// [MODIFIED] - 添加缺失的 setPosition 方法声明
  /// 这是解决 "The method 'setPosition' isn't defined" 错误的关键
  external setPosition(AmpJsLngLat lngLat);
}

@JS()
class Control {
  external Control();
}

@JS()
class Scale extends Control {
  external Scale();
}

@JS()
class ToolBar extends Control {
  external ToolBar(ControlConfig opts);
}

@JS('Icon')
class AMapIcon {
  external AMapIcon(IconOptions options);
}

@JS()
class Size {
  external Size(num width, num height);
}

@JS()
@anonymous
class MapsEvent {
  external AmpJsLngLat get lnglat;
}

@JS()
@anonymous
class MapOptions {
  external factory MapOptions({
    /// 初始中心经纬度
    AmpJsLngLat center,
    bool resizeEnable,

    /// 地图显示的缩放级别
    num zoom,

    /// 地图视图模式, 默认为‘2D’
    String /*‘2D’|‘3D’*/ viewMode,
  });

  external AmpJsLngLat get center;

  external num get zoom;

  external String get viewMode;
}

@JS()
@anonymous
class MarkerOptions {
  external factory MarkerOptions({
    AMap map,
    AmpJsLngLat position,
    AMapIcon icon,
    String title,
    Pixel offset,
    String anchor,
  });

  external AmpJsLngLat get position;

  external set position(AmpJsLngLat v);
}

@JS()
@anonymous
class ControlConfig {
  external factory ControlConfig({
    String position,
    List<num> offset,
  });

  external String get position;

  external List<num> get offset;

  external set position(String v);

  external set offset(List<num> v);
}

@JS()
@anonymous
class GeolocationOptions {
  external factory GeolocationOptions({
    /// 是否使用高精度定位，默认：true 。2.0 默认为false
    bool enableHighAccuracy,

    /// 设置定位超时时间，默认：无穷大
    int timeout,

    /// 定位按钮的停靠位置的偏移量，默认：Pixel(10, 20)。 2.0为offset
    Pixel buttonOffset,

    ///  定位成功后调整地图视野范围使定位位置及精度范围视野内可见，默认：false
    bool zoomToAccuracy,

    ///  定位按钮的排放位置, 'LT': 左上角, 'RT': 右上角, 'LB': 左下角, 'RB': 右下角。 2.0为position
    String /*‘LT’|‘RT’|‘LB’|‘RB’*/ buttonPosition,
  });
}

@JS()
@anonymous
class PlaceSearchOptions {
  external factory PlaceSearchOptions({
    ///此项默认值：base，返回基本地址信息
    /// 取值：all，返回基本+详细信息
    String extensions,
    String type,
    String city,
    bool citylimit,
    int pageSize,
    int pageIndex,
  });
}

@JS()
@anonymous
class IconOptions {
  external factory IconOptions({
    Size size,
    String image,
    Size imageSize,
  });
}

@JS()
@anonymous
class GeolocationResult {
  external AmpJsLngLat get position;

  external String get message;
}

@JS()
@anonymous
class SearchResult {
  external PoiList? get poiList;

  /// 成功状态说明
  external String get info;
}

@JS()
@anonymous
class PoiList {
  external List<dynamic>? get pois;

  /// 查询结果总数
  external int get count;
}

@JS()
@anonymous
class Poi {
  external String get citycode;

  external String get cityname;

  external String get pname;

  external String get pcode;

  external AmpJsLngLat get location;

  external String get adname;

  external String get name;

  external String get address;
}

@JS()
@anonymous
class GeocoderOptions {
  external factory GeocoderOptions({
    ///城市，地理编码时，设置地址描述所在城市
    /// 可选值：城市名（中文或中文全拼）、citycode、adcode
    /// 默认值：“全国”
    String city,

    /// 逆地理编码时，以给定坐标为中心点，单位：米
    /// 取值范围：0 - 3000
    /// 默认值：1000
    String radius,

    ///是否批量查询
    /// batch 设置为 false 时，只返回第一条记录
    bool batch,

    ///逆地理编码时，返回信息的详略
    /// 默认值：base，返回基本地址信息
    /// 取值为：all，返回地址信息及附近poi、道路、道路交叉口等信息
    String extensions,
  });
}
