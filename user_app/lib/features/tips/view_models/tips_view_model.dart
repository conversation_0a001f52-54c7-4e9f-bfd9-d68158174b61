import 'package:flutter/material.dart';
import 'package:user_app/models/moment/moment_list_request.dart';
import 'package:user_app/models/moment/moment_vo.dart';
import 'package:user_app/services/moment_service.dart';

class TipsViewModel extends ChangeNotifier {
  final MomentService _momentService;

  TipsViewModel(this._momentService);

  // State management
  List<MomentVo> _tips = [];
  bool _isLoading = false;
  bool _hasMore = true;
  String? _errorMessage;
  int _currentPage = 1;
  static const int _pageSize = 10;

  // Filters - focus on technique-related tips
  String _selectedTipType = "全部";
  List<String> _selectedTags = [];
  String _searchQuery = "";

  // Filter options for tips
  final List<String> _tipTypeFilters = ["全部", "技巧分享", "装备推荐", "钓点攻略", "经验心得"];

  // Getters
  List<MomentVo> get tips => _tips;

  bool get isLoading => _isLoading;

  bool get hasMore => _hasMore;

  String? get errorMessage => _errorMessage;

  String get selectedTipType => _selectedTipType;

  List<String> get selectedTags => _selectedTags;

  String get searchQuery => _searchQuery;

  List<String> get tipTypeFilters => _tipTypeFilters;

  Future<void> loadTips({bool refresh = false}) async {
    if (refresh) {
      _currentPage = 1;
      _hasMore = true;
      _tips.clear();
    }

    if (_isLoading || !_hasMore) return;

    _isLoading = true;
    _errorMessage = null;
    notifyListeners();

    try {
      final request = MomentListRequest(
        pageNum: _currentPage,
        pageSize: _pageSize,
        tag: _selectedTags.isNotEmpty ? _selectedTags.first : null,
      );

      final response = await _momentService.getMoments(request);

      // Filter for technique-related content
      final filteredTips = response.records.where((moment) {
        return moment.momentType == "technique" ||
            moment.momentType == "equipment";
        // Note: tag field no longer exists in the new model
      }).toList();

      if (refresh) {
        _tips = filteredTips;
      } else {
        _tips.addAll(filteredTips);
      }

      _hasMore = _currentPage < response.pages;
      _currentPage++;
      _errorMessage = null;
    } catch (e) {
      _errorMessage = e.toString();
      print('Error loading tips: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> searchTips(String query) async {
    _searchQuery = query;
    await loadTips(refresh: true);
  }

  void setTipTypeFilter(String tipType) {
    _selectedTipType = tipType;
    loadTips(refresh: true);
  }

  void toggleTag(String tag) {
    if (_selectedTags.contains(tag)) {
      _selectedTags.remove(tag);
    } else {
      _selectedTags.add(tag);
    }
    loadTips(refresh: true);
  }

  void clearAllFilters() {
    _selectedTipType = "全部";
    _selectedTags.clear();
    _searchQuery = "";
    loadTips(refresh: true);
  }

  // Like/unlike functionality
  Future<void> toggleLikeTip(int tipId) async {
    try {
      final tipIndex = _tips.indexWhere((t) => t.id == tipId);
      if (tipIndex == -1) return;

      final tip = _tips[tipIndex];
      final wasLiked = tip.isLiked ?? false;

      // Optimistic update
      final updatedTip = tip.copyWith(
        isLiked: !wasLiked,
        likeCount: (tip.likeCount ?? 0) + (wasLiked ? -1 : 1),
      );
      _tips[tipIndex] = updatedTip;
      notifyListeners();

      // API call
      await _momentService.likeMoment(
        tipId,
        !wasLiked, // true for like, false for unlike
      );
    } catch (e) {
      // Revert on error
      loadTips(refresh: true);
      print('Error toggling like: $e');
    }
  }

  int getActiveFilterCount() {
    int count = 0;
    if (_selectedTipType != "全部") count++;
    if (_selectedTags.isNotEmpty) count += _selectedTags.length;
    return count;
  }

  void reset() {
    _tips.clear();
    _currentPage = 0;
    _hasMore = true;
    _isLoading = false;
    _errorMessage = null;
    _selectedTipType = "全部";
    _selectedTags.clear();
    _searchQuery = "";
    notifyListeners();
  }
}
