import 'package:flutter/material.dart';
import 'package:user_app/models/comment/comment_vo.dart';

class CommentItem extends StatelessWidget {
  final CommentVo comment;
  final VoidCallback onReply;
  final Function(bool isUpvote) onVote;

  const CommentItem({
    super.key,
    required this.comment,
    required this.onReply,
    required this.onVote,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 主评论
          _buildCommentContent(context),

          // 子评论
          if (comment.replies.isNotEmpty) ...[
            const SizedBox(height: 12),
            Container(
              margin: const EdgeInsets.only(left: 48),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                children: comment.replies
                    .map((subComment) => Padding(
                          padding: const EdgeInsets.only(bottom: 8),
                          child: _buildSubCommentContent(context, subComment),
                        ))
                    .toList(),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildCommentContent(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 头像
        Container(
          width: 36,
          height: 36,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: Colors.grey.shade200,
            image: comment.userAvatar != null
                ? DecorationImage(
                    image: NetworkImage(comment.userAvatar!),
                    fit: BoxFit.cover,
                  )
                : null,
          ),
          child: comment.userAvatar == null
              ? Icon(
                  Icons.person,
                  color: Colors.grey.shade400,
                  size: 20,
                )
              : null,
        ),
        const SizedBox(width: 12),

        // 评论内容
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 用户名和时间
              Row(
                children: [
                  Text(
                    comment.userName,
                    style: const TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: 14,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    _formatTime(comment.createdAt),
                    style: TextStyle(
                      color: Colors.grey.shade500,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 6),

              // 评论文本
              Text(
                comment.content,
                style: const TextStyle(
                  fontSize: 14,
                  height: 1.4,
                ),
              ),
              const SizedBox(height: 8),

              // 操作按钮
              Row(
                children: [
                  // 点赞
                  _buildActionButton(
                    icon: comment.upVoted == true
                        ? Icons.thumb_up
                        : Icons.thumb_up_outlined,
                    count: comment.upVotes.toInt(),
                    isActive: comment.upVoted == true,
                    onTap: () => onVote(true),
                  ),
                  const SizedBox(width: 16),

                  // 踩
                  _buildActionButton(
                    icon: comment.downVoted == true
                        ? Icons.thumb_down
                        : Icons.thumb_down_outlined,
                    count: comment.downVotes.toInt(),
                    isActive: comment.downVoted == true,
                    onTap: () => onVote(false),
                  ),
                  const SizedBox(width: 16),

                  // 回复
                  _buildActionButton(
                    icon: Icons.reply_outlined,
                    text: '回复',
                    onTap: onReply,
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildSubCommentContent(BuildContext context, CommentVo subComment) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 小头像
        Container(
          width: 24,
          height: 24,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: Colors.grey.shade200,
            image: subComment.userAvatar != null
                ? DecorationImage(
                    image: NetworkImage(subComment.userAvatar!),
                    fit: BoxFit.cover,
                  )
                : null,
          ),
          child: subComment.userAvatar == null
              ? Icon(
                  Icons.person,
                  color: Colors.grey.shade400,
                  size: 12,
                )
              : null,
        ),
        const SizedBox(width: 8),

        // 回复内容
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 用户名和时间
              Row(
                children: [
                  Text(
                    subComment.userName,
                    style: const TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: 12,
                    ),
                  ),
                  const SizedBox(width: 6),
                  Text(
                    _formatTime(subComment.createdAt),
                    style: TextStyle(
                      color: Colors.grey.shade500,
                      fontSize: 11,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 4),

              // 回复文本
              Text(
                subComment.content,
                style: const TextStyle(
                  fontSize: 13,
                  height: 1.3,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    int? count,
    String? text,
    bool isActive = false,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 16,
            color: isActive ? Colors.blue.shade600 : Colors.grey.shade600,
          ),
          if (count != null && count > 0) ...[
            const SizedBox(width: 4),
            Text(
              count.toString(),
              style: TextStyle(
                fontSize: 12,
                color: isActive ? Colors.blue.shade600 : Colors.grey.shade600,
              ),
            ),
          ],
          if (text != null) ...[
            const SizedBox(width: 4),
            Text(
              text,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade600,
              ),
            ),
          ],
        ],
      ),
    );
  }

  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return '${difference.inDays}天前';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}小时前';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}分钟前';
    } else {
      return '刚刚';
    }
  }
}
