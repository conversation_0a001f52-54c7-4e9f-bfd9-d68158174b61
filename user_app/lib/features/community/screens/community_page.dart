import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:user_app/config/app_routes.dart';
import 'package:user_app/features/community/view_models/community_view_model.dart';
import 'package:user_app/features/moments/screens/moment_detail_page.dart';
import 'package:user_app/models/moment/moment_vo.dart';
import 'package:user_app/view_models/auth_view_model.dart';
import 'package:user_app/widgets/moments_list.dart';

class CommunityPage extends StatefulWidget {
  const CommunityPage({super.key});

  @override
  State<CommunityPage> createState() => _CommunityPageState();
}

class _CommunityPageState extends State<CommunityPage>
    with SingleTickerProviderStateMixin {
  late CommunityViewModel _communityViewModel;
  late AnimationController _fabAnimationController;

  bool _isFabVisible = true;
  bool _previousAuthState = false;

  @override
  void initState() {
    super.initState();
    _communityViewModel = context.read<CommunityViewModel>();
    _fabAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 200),
    );
    _fabAnimationController.forward();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _communityViewModel.loadMoments(refresh: true);
      _previousAuthState = context.read<AuthViewModel>().isUserLoggedIn();
    });
  }

  @override
  void dispose() {
    _fabAnimationController.dispose();
    super.dispose();
  }

  final List<String> _filterOptions = ["全部", "钓获分享", "装备展示", "技巧分享", "问答求助"];

  @override
  Widget build(BuildContext context) {
    final authViewModel = context.watch<AuthViewModel>();
    final currentAuthState = authViewModel.isUserLoggedIn();

    if (_previousAuthState != currentAuthState) {
      _previousAuthState = currentAuthState;
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          _communityViewModel.loadMoments(refresh: true);
        }
      });
    }

    final communityViewModel = context.watch<CommunityViewModel>();
    final isLoading = communityViewModel.isLoading;
    final moments = communityViewModel.moments;
    final hasMore = communityViewModel.hasMore;
    final hasSelectedTags = communityViewModel.selectedTags.isNotEmpty;

    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      body: CustomScrollView(
        physics: const BouncingScrollPhysics(),
        slivers: [
          SliverAppBar(
            floating: true,
            backgroundColor: Colors.white,
            elevation: 0,
            automaticallyImplyLeading: false,
            title: const Text(
              '社区',
              style: TextStyle(
                color: Colors.black87,
                fontWeight: FontWeight.w600,
              ),
            ),
            actions: [
              IconButton(
                onPressed: _showSortOptions,
                icon: Icon(
                  communityViewModel.sortBy == 'latest'
                      ? Icons.schedule
                      : Icons.local_fire_department,
                  color: Theme.of(context).primaryColor,
                ),
              ),
              IconButton(
                onPressed: () => context.push(AppRoutes.search),
                icon: Icon(Icons.search, color: Colors.grey.shade700),
              ),
              IconButton(
                onPressed: _showNotifications,
                icon: Stack(
                  children: [
                    Icon(Icons.notifications_outlined,
                        color: Colors.grey.shade700),
                    // TODO: Replace with real logic e.g., notificationViewModel.hasUnread
                    if (true)
                      Positioned(
                        top: 8,
                        right: 8,
                        child: Container(
                          width: 8,
                          height: 8,
                          decoration: const BoxDecoration(
                            color: Colors.red,
                            shape: BoxShape.circle,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
              const SizedBox(width: 8),
            ],
          ),
          SliverPersistentHeader(
            pinned: true,
            delegate: _OptimizedFilterBarDelegate(
              minHeight: 48,
              maxHeight: hasSelectedTags ? 84 : 48,
              child: Container(
                color: Colors.white,
                child: Column(
                  children: [
                    Container(
                      height: 48,
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: Row(
                        children: [
                          if (isAuthenticated())
                            GestureDetector(
                              onTap: () {
                                HapticFeedback.selectionClick();
                                // Call the correct ViewModel method
                                final newValue =
                                    !communityViewModel.showOnlyFollowing;
                                communityViewModel
                                    .setShowOnlyFollowing(newValue);
                              },
                              child: Container(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 12, vertical: 6),
                                decoration: BoxDecoration(
                                  color: communityViewModel.showOnlyFollowing
                                      ? Theme.of(context).primaryColor
                                      : Colors.grey.shade100,
                                  borderRadius: BorderRadius.circular(20),
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Icon(
                                      communityViewModel.showOnlyFollowing
                                          ? Icons.people
                                          : Icons.people_outline,
                                      size: 16,
                                      color:
                                          communityViewModel.showOnlyFollowing
                                              ? Colors.white
                                              : Colors.grey.shade700,
                                    ),
                                    const SizedBox(width: 4),
                                    Text(
                                      '关注',
                                      style: TextStyle(
                                        fontSize: 13,
                                        color:
                                            communityViewModel.showOnlyFollowing
                                                ? Colors.white
                                                : Colors.grey.shade700,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          if (isAuthenticated()) const SizedBox(width: 8),
                          Expanded(
                            child: SingleChildScrollView(
                              scrollDirection: Axis.horizontal,
                              child: Row(
                                children: _filterOptions.map((filter) {
                                  final isSelected =
                                      communityViewModel.selectedMomentType ==
                                          filter;
                                  return Padding(
                                    padding: const EdgeInsets.only(right: 8),
                                    child: GestureDetector(
                                      onTap: () {
                                        HapticFeedback.selectionClick();
                                        communityViewModel
                                            .setMomentTypeFilter(filter);
                                      },
                                      child: Container(
                                        padding: const EdgeInsets.symmetric(
                                            horizontal: 16, vertical: 8),
                                        decoration: BoxDecoration(
                                          color: isSelected
                                              ? Theme.of(context).primaryColor
                                              : Colors.transparent,
                                          borderRadius:
                                              BorderRadius.circular(20),
                                          border: Border.all(
                                            color: isSelected
                                                ? Theme.of(context).primaryColor
                                                : Colors.grey.shade300,
                                          ),
                                        ),
                                        child: Text(
                                          filter,
                                          style: TextStyle(
                                            color: isSelected
                                                ? Colors.white
                                                : Colors.grey.shade700,
                                            fontWeight: FontWeight.w500,
                                          ),
                                        ),
                                      ),
                                    ),
                                  );
                                }).toList(),
                              ),
                            ),
                          ),
                          const SizedBox(width: 8),
                          GestureDetector(
                            onTap: _showAdvancedFilters,
                            child: Container(
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                color: hasSelectedTags
                                    ? Theme.of(context)
                                        .primaryColor
                                        .withOpacity(0.1)
                                    : Colors.grey.shade100,
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Badge(
                                isLabelVisible: hasSelectedTags,
                                label: Text(communityViewModel
                                    .selectedTags.length
                                    .toString()),
                                child: Icon(
                                  Icons.filter_list,
                                  size: 20,
                                  color: hasSelectedTags
                                      ? Theme.of(context).primaryColor
                                      : Colors.grey.shade700,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    if (hasSelectedTags)
                      Container(
                        height: 36,
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        child: ListView(
                          scrollDirection: Axis.horizontal,
                          children: communityViewModel.selectedTags
                              .map((tag) => Container(
                                    margin: const EdgeInsets.only(right: 8),
                                    child: Chip(
                                      label: Text(tag),
                                      deleteIcon:
                                          const Icon(Icons.close, size: 16),
                                      onDeleted: () {
                                        communityViewModel.toggleTag(tag);
                                      },
                                      backgroundColor: Theme.of(context)
                                          .primaryColor
                                          .withOpacity(0.1),
                                      deleteIconColor:
                                          Theme.of(context).primaryColor,
                                      labelStyle: TextStyle(
                                        color: Theme.of(context).primaryColor,
                                        fontSize: 12,
                                      ),
                                      padding: EdgeInsets.zero,
                                      visualDensity: VisualDensity.compact,
                                    ),
                                  ))
                              .toList(),
                        ),
                      ),
                  ],
                ),
              ),
            ),
          ),
          SliverFillRemaining(
            child: MomentsList(
              moments: moments,
              isLoading: isLoading,
              hasMore: hasMore,
              onRefresh: () => communityViewModel.loadMoments(refresh: true),
              onLoadMore: () => communityViewModel.loadMoments(),
              onMomentTap: _showMomentDetail,
              onUserTap: _showUserProfile,
              onLikeTap: _toggleLike,
              showSpotInfo: true,
              emptyWidget: _buildEmptyWidget(),
              padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
            ),
          ),
        ],
      ),
      floatingActionButton: ScaleTransition(
        scale: _fabAnimationController,
        child: FloatingActionButton.extended(
          onPressed: _showPublishOptions,
          backgroundColor: Theme.of(context).primaryColor,
          icon: const Icon(Icons.edit),
          label: const Text('发布'),
        ),
      ),
    );
  }

  Widget _buildEmptyWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.bubble_chart_outlined,
              size: 80, color: Colors.grey.shade300),
          const SizedBox(height: 16),
          Text(
            _communityViewModel.showOnlyFollowing ? '还没有关注的人发布动态' : '暂无动态',
            style: TextStyle(color: Colors.grey.shade600, fontSize: 16),
          ),
          const SizedBox(height: 8),
          Text(
            _communityViewModel.showOnlyFollowing ? '去发现更多有趣的钓友吧' : '成为第一个分享的人',
            style: TextStyle(color: Colors.grey.shade500, fontSize: 14),
          ),
          const SizedBox(height: 24),
          OutlinedButton.icon(
            onPressed: _communityViewModel.showOnlyFollowing
                ? () => _communityViewModel.setShowOnlyFollowing(false)
                : _showPublishOptions,
            icon: Icon(_communityViewModel.showOnlyFollowing
                ? Icons.explore
                : Icons.add),
            label:
                Text(_communityViewModel.showOnlyFollowing ? '发现动态' : '发布动态'),
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
          ),
        ],
      ),
    );
  }

  void _showSortOptions() {
    final currentSortBy = context.read<CommunityViewModel>().sortBy;
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return Container(
          decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.vertical(top: Radius.circular(20))),
          child: SafeArea(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 40,
                  height: 4,
                  margin: const EdgeInsets.symmetric(vertical: 12),
                  decoration: BoxDecoration(
                      color: Colors.grey.shade300,
                      borderRadius: BorderRadius.circular(2)),
                ),
                const Padding(
                  padding: EdgeInsets.all(16),
                  child: Text('排序方式',
                      style:
                          TextStyle(fontSize: 18, fontWeight: FontWeight.w600)),
                ),
                ListTile(
                  leading: const Icon(Icons.schedule),
                  title: const Text('最新发布'),
                  trailing: currentSortBy == 'latest'
                      ? Icon(Icons.check, color: Theme.of(context).primaryColor)
                      : null,
                  onTap: () {
                    Navigator.pop(context);
                    _communityViewModel.setSortBy('latest');
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.local_fire_department),
                  title: const Text('最热门'),
                  subtitle: const Text('按互动量排序'),
                  trailing: currentSortBy == 'hottest'
                      ? Icon(Icons.check, color: Theme.of(context).primaryColor)
                      : null,
                  onTap: () {
                    Navigator.pop(context);
                    _communityViewModel.setSortBy('hottest');
                  },
                ),
                const SizedBox(height: 16),
              ],
            ),
          ),
        );
      },
    );
  }

  void _showAdvancedFilters() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) {
        final List<String> localSelectedTags =
            List.from(_communityViewModel.selectedTags);
        return StatefulBuilder(
          builder: (context, setModalState) {
            return Container(
              height: MediaQuery.of(context).size.height * 0.6,
              decoration: const BoxDecoration(
                  color: Colors.white,
                  borderRadius:
                      BorderRadius.vertical(top: Radius.circular(20))),
              child: Column(
                children: [
                  Container(
                    padding: const EdgeInsets.all(16),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text('标签筛选',
                            style: TextStyle(
                                fontSize: 18, fontWeight: FontWeight.w600)),
                        TextButton(
                          onPressed: () =>
                              setModalState(() => localSelectedTags.clear()),
                          child: const Text('清除'),
                        ),
                      ],
                    ),
                  ),
                  Expanded(
                    child: SingleChildScrollView(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: Wrap(
                        spacing: 8,
                        runSpacing: 8,
                        children: [
                          '新手入门',
                          '路亚技巧',
                          '台钓',
                          '野钓',
                          '黑坑',
                          '饵料配方',
                          '装备推荐',
                          '钓点分享',
                          '冬季钓鱼',
                          '鲫鱼',
                          '鲤鱼',
                          '草鱼',
                          '鲢鳙',
                          '翘嘴',
                          '鲈鱼'
                        ].map((tag) {
                          final isSelected = localSelectedTags.contains(tag);
                          return FilterChip(
                            label: Text(tag),
                            selected: isSelected,
                            onSelected: (selected) {
                              setModalState(() {
                                if (selected) {
                                  localSelectedTags.add(tag);
                                } else {
                                  localSelectedTags.remove(tag);
                                }
                              });
                            },
                          );
                        }).toList(),
                      ),
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.all(16),
                    child: SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: () {
                          Navigator.pop(context);
                          // Call the new method in the ViewModel
                          _communityViewModel
                              .setSelectedTags(localSelectedTags);
                        },
                        style: ElevatedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12)),
                        ),
                        child:
                            const Text('应用筛选', style: TextStyle(fontSize: 16)),
                      ),
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  void _showPublishOptions() {
    if (!isAuthenticated()) {
      _showLoginDialog('发布动态');
      return;
    }
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return Container(
          decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.vertical(top: Radius.circular(20))),
          child: SafeArea(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 40,
                  height: 4,
                  margin: const EdgeInsets.symmetric(vertical: 12),
                  decoration: BoxDecoration(
                      color: Colors.grey.shade300,
                      borderRadius: BorderRadius.circular(2)),
                ),
                const Padding(
                  padding: EdgeInsets.all(16),
                  child: Text('发布动态',
                      style:
                          TextStyle(fontSize: 18, fontWeight: FontWeight.w600)),
                ),
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          _buildPublishOption(
                              icon: Icons.set_meal,
                              label: '钓获分享',
                              color: Colors.green,
                              onTap: () {
                                Navigator.pop(context);
                                context.push(AppRoutes.publishMoment,
                                    extra: '钓获分享');
                              }),
                          _buildPublishOption(
                              icon: Icons.backpack,
                              label: '装备展示',
                              color: Colors.blue,
                              onTap: () {
                                Navigator.pop(context);
                                context.push(AppRoutes.publishMoment,
                                    extra: '装备展示');
                              }),
                        ],
                      ),
                      const SizedBox(height: 16),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          _buildPublishOption(
                              icon: Icons.tips_and_updates,
                              label: '技巧分享',
                              color: Colors.orange,
                              onTap: () {
                                Navigator.pop(context);
                                context.push(AppRoutes.publishMoment,
                                    extra: '技巧分享');
                              }),
                          _buildPublishOption(
                              icon: Icons.help_outline,
                              label: '问答求助',
                              color: Colors.purple,
                              onTap: () {
                                Navigator.pop(context);
                                context.push(AppRoutes.publishMoment,
                                    extra: '问答求助');
                              }),
                        ],
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildPublishOption(
      {required IconData icon,
      required String label,
      required Color color,
      required VoidCallback onTap}) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            width: 64,
            height: 64,
            decoration: BoxDecoration(
                color: color.withOpacity(0.1),
                borderRadius: BorderRadius.circular(16)),
            child: Icon(icon, color: color, size: 32),
          ),
          const SizedBox(height: 8),
          Text(label,
              style: TextStyle(fontSize: 13, color: Colors.grey.shade700)),
        ],
      ),
    );
  }

  void _showNotifications() {
    if (!isAuthenticated()) {
      _showLoginDialog('查看通知');
      return;
    }
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return Container(
          height: MediaQuery.of(context).size.height * 0.8,
          decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.vertical(top: Radius.circular(20))),
          child: Column(
            children: [
              Container(
                width: 40,
                height: 4,
                margin: const EdgeInsets.symmetric(vertical: 12),
                decoration: BoxDecoration(
                    color: Colors.grey.shade300,
                    borderRadius: BorderRadius.circular(2)),
              ),
              Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text('通知',
                        style: TextStyle(
                            fontSize: 18, fontWeight: FontWeight.w600)),
                    TextButton(onPressed: () {}, child: const Text('全部已读')),
                  ],
                ),
              ),
              Expanded(
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.notifications_none,
                          size: 80, color: Colors.grey.shade300),
                      const SizedBox(height: 16),
                      Text('暂无新通知',
                          style: TextStyle(
                              color: Colors.grey.shade600, fontSize: 16)),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  void _showMomentDetail(moment) async {
    final result = await Navigator.of(context).push<MomentVo>(
      MaterialPageRoute(
        builder: (context) => MomentDetailPage(
          momentId: moment.id,
          initialMoment: moment,
        ),
      ),
    );

    // If the moment was updated in the detail page, sync it back to the list
    if (result != null) {
      _communityViewModel.updateMoment(result);
    }
  }

  void _showUserProfile(user) {
    context.pushNamed('user_profile',
        pathParameters: {'userId': user.id.toString()}, extra: user);
  }

  void _toggleLike(int momentId) async {
    if (!isAuthenticated()) {
      _showLoginDialog('点赞动态');
      return;
    }
    try {
      await _communityViewModel.toggleLikeMoment(momentId);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
              content: Text('点赞失败: $e'),
              behavior: SnackBarBehavior.floating,
              duration: const Duration(seconds: 2)),
        );
      }
    }
  }

  void _showLoginDialog(String action) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          title: const Text('需要登录'),
          content: Text('您需要先登录才能$action'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('取消'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                context.push(AppRoutes.login);
              },
              style: ElevatedButton.styleFrom(
                  shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8))),
              child: const Text('去登录'),
            ),
          ],
        );
      },
    );
  }

  bool isAuthenticated() {
    return context.read<AuthViewModel>().isUserLoggedIn();
  }
}

class _OptimizedFilterBarDelegate extends SliverPersistentHeaderDelegate {
  final Widget child;
  final double minHeight;
  final double maxHeight;

  _OptimizedFilterBarDelegate({
    required this.child,
    required this.minHeight,
    required this.maxHeight,
  });

  @override
  Widget build(
      BuildContext context, double shrinkOffset, bool overlapsContent) {
    return Material(
      elevation: overlapsContent ? 2 : 0,
      shadowColor: Colors.black.withOpacity(0.1),
      child: child,
    );
  }

  @override
  double get maxExtent => maxHeight;

  @override
  double get minExtent => minHeight;

  @override
  bool shouldRebuild(covariant _OptimizedFilterBarDelegate oldDelegate) {
    return maxHeight != oldDelegate.maxHeight ||
        minHeight != oldDelegate.minHeight ||
        child != oldDelegate.child;
  }
}
