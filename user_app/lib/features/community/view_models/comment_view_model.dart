import 'package:flutter/foundation.dart';
import 'package:user_app/models/comment/comment_vo.dart';
import 'package:user_app/services/comment_service.dart';
import 'package:user_app/constants/vote_type.dart';

// 评论投票状态类，用于保存和恢复状态
class CommentVoteState {
  final bool upVoted;
  final bool downVoted;
  final int upVotes;
  final int downVotes;

  CommentVoteState({
    required this.upVoted,
    required this.downVoted,
    required this.upVotes,
    required this.downVotes,
  });
}

class CommentViewModel extends ChangeNotifier {
  final CommentService _commentService;

  CommentViewModel(this._commentService);

  // 评论数据，按动态ID分组
  final Map<int, List<CommentVo>> _commentsByMoment = {};
  bool _isLoading = false;

  // Getters
  bool get isLoading => _isLoading;

  List<CommentVo> getCommentsForMoment(int momentId) {
    return _commentsByMoment[momentId] ?? [];
  }

  // 加载评论
  Future<void> loadComments(int momentId) async {
    _isLoading = true;
    notifyListeners();

    try {
      final comments = await _commentService.fetchComments(momentId);
      _commentsByMoment[momentId] = comments;
    } catch (e) {
      debugPrint('Load comments error: $e');
      _commentsByMoment[momentId] = [];
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // 添加评论
  Future<void> addComment(int momentId, String content) async {
    try {
      await _commentService.addComment(momentId, content);
      // 重新加载评论列表
      await loadComments(momentId);
    } catch (e) {
      debugPrint('Add comment error: $e');
      rethrow;
    }
  }

  // 回复评论
  Future<void> replyToComment(
      int momentId, int commentId, String content) async {
    try {
      await _commentService.replyToComment(momentId, commentId, content);
      // 重新加载评论列表
      await loadComments(momentId);
    } catch (e) {
      debugPrint('Reply to comment error: $e');
      rethrow;
    }
  }

  // 评论点赞/踩
  Future<void> voteComment(int commentId, bool isUpvote) async {
    // 先保存当前状态，用于错误时回滚
    final originalState = _getCommentVoteState(commentId);

    // 立即更新UI（乐观更新）
    _updateCommentVoteStatus(commentId, isUpvote);
    notifyListeners();

    try {
      // 然后调用API
      final voteType = isUpvote ? VoteType.upVote : VoteType.downVote;
      await _commentService.voteComment(commentId, voteType);
    } catch (e) {
      // 如果API调用失败，回滚到原始状态
      if (originalState != null) {
        _restoreCommentVoteState(commentId, originalState);
        notifyListeners();
      }
      debugPrint('Vote comment error: $e');
      rethrow;
    }
  }

  // 更新评论的投票状态
  void _updateCommentVoteStatus(int commentId, bool isUpvote) {
    for (final comments in _commentsByMoment.values) {
      for (final comment in comments) {
        if (comment.id == commentId) {
          if (isUpvote) {
            // 点赞
            if (comment.upVoted == true) {
              // 取消点赞
              comment.upVoted = false;
              comment.upVotes = comment.upVotes - 1;
            } else {
              // 点赞
              comment.upVoted = true;
              comment.upVotes = comment.upVotes + 1;
              // 如果之前踩过，取消踩
              if (comment.downVoted == true) {
                comment.downVoted = false;
                comment.downVotes = comment.downVotes - 1;
              }
            }
          } else {
            // 踩
            if (comment.downVoted == true) {
              // 取消踩
              comment.downVoted = false;
              comment.downVotes = comment.downVotes - 1;
            } else {
              // 踩
              comment.downVoted = true;
              comment.downVotes = comment.downVotes + 1;
              // 如果之前点赞过，取消点赞
              if (comment.upVoted == true) {
                comment.upVoted = false;
                comment.upVotes = comment.upVotes - 1;
              }
            }
          }
          return;
        }

        // 检查子评论
        for (final subComment in comment.replies) {
          if (subComment.id == commentId) {
            if (isUpvote) {
              if (subComment.upVoted == true) {
                subComment.upVoted = false;
                subComment.upVotes = subComment.upVotes - 1;
              } else {
                subComment.upVoted = true;
                subComment.upVotes = subComment.upVotes + 1;
                if (subComment.downVoted == true) {
                  subComment.downVoted = false;
                  subComment.downVotes = subComment.downVotes - 1;
                }
              }
            } else {
              if (subComment.downVoted == true) {
                subComment.downVoted = false;
                subComment.downVotes = subComment.downVotes - 1;
              } else {
                subComment.downVoted = true;
                subComment.downVotes = subComment.downVotes + 1;
                if (subComment.upVoted == true) {
                  subComment.upVoted = false;
                  subComment.upVotes = subComment.upVotes - 1;
                }
              }
            }
            return;
          }
        }
      }
    }
  }

  // 清除特定动态的评论
  void clearCommentsForMoment(int momentId) {
    _commentsByMoment.remove(momentId);
    notifyListeners();
  }

  // 清除所有评论
  void clearAllComments() {
    _commentsByMoment.clear();
    notifyListeners();
  }

  // 获取评论的当前投票状态
  CommentVoteState? _getCommentVoteState(int commentId) {
    for (final comments in _commentsByMoment.values) {
      for (final comment in comments) {
        if (comment.id == commentId) {
          return CommentVoteState(
            upVoted: comment.upVoted,
            downVoted: comment.downVoted,
            upVotes: comment.upVotes,
            downVotes: comment.downVotes,
          );
        }

        // 检查子评论
        for (final subComment in comment.replies) {
          if (subComment.id == commentId) {
            return CommentVoteState(
              upVoted: subComment.upVoted,
              downVoted: subComment.downVoted,
              upVotes: subComment.upVotes,
              downVotes: subComment.downVotes,
            );
          }
        }
      }
    }
    return null;
  }

  // 恢复评论的投票状态
  void _restoreCommentVoteState(int commentId, CommentVoteState state) {
    for (final comments in _commentsByMoment.values) {
      for (final comment in comments) {
        if (comment.id == commentId) {
          comment.upVoted = state.upVoted;
          comment.downVoted = state.downVoted;
          comment.upVotes = state.upVotes;
          comment.downVotes = state.downVotes;
          return;
        }

        // 检查子评论
        for (final subComment in comment.replies) {
          if (subComment.id == commentId) {
            subComment.upVoted = state.upVoted;
            subComment.downVoted = state.downVoted;
            subComment.upVotes = state.upVotes;
            subComment.downVotes = state.downVotes;
            return;
          }
        }
      }
    }
  }
}
