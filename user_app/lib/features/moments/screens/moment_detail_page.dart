import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:user_app/config/app_routes.dart';
import 'package:user_app/features/community/view_models/comment_view_model.dart';
import 'package:user_app/features/community/widgets/comment_section.dart';
import 'package:user_app/features/fishing_spots/utils/moment_type_data_parser.dart';
import 'package:user_app/models/moment/moment_vo.dart';
import 'package:user_app/services/moment_service.dart';
import 'package:user_app/view_models/auth_view_model.dart';

class MomentDetailPage extends StatefulWidget {
  final int momentId;
  final MomentVo? initialMoment;

  const MomentDetailPage({
    super.key,
    required this.momentId,
    this.initialMoment,
  });

  @override
  State<MomentDetailPage> createState() => _MomentDetailPageState();
}

class _MomentDetailPageState extends State<MomentDetailPage>
    with WidgetsBindingObserver {
  MomentVo? moment;
  bool isLoading = false;
  String? error;

  final TextEditingController _commentController = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  bool _isReplying = false;
  int? _replyToCommentId;
  String? _replyToUserName;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    if (widget.initialMoment != null) {
      moment = widget.initialMoment;
    } else {
      _loadMomentDetail();
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _commentController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.resumed && mounted) {
      _loadMomentDetail();
    }
  }

  Future<void> _loadMomentDetail() async {
    setState(() => isLoading = true);
    try {
      final momentService = Provider.of<MomentService>(context, listen: false);
      final momentDetail = await momentService.getById(widget.momentId);
      if (mounted) {
        setState(() {
          moment = momentDetail;
          isLoading = false;
          error = null;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          error = e.toString();
          isLoading = false;
        });
      }
    }
  }

  bool get _isAuthenticated {
    return context.read<AuthViewModel>().isUserLoggedIn();
  }

  void _showLoginDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: const Text('需要登录'),
        content: const Text('请先登录后再进行评论'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              context.push(AppRoutes.login);
            },
            child: const Text('去登录'),
          ),
        ],
      ),
    );
  }

  void _startReply(int commentId, String userName) {
    if (!_isAuthenticated) {
      _showLoginDialog();
      return;
    }
    setState(() {
      _isReplying = true;
      _replyToCommentId = commentId;
      _replyToUserName = userName;
    });
    _focusNode.requestFocus();
  }

  void _cancelReply() {
    setState(() {
      _isReplying = false;
      _replyToCommentId = null;
      _replyToUserName = null;
    });
    _commentController.clear();
  }

  Future<void> _submitComment() async {
    if (!_isAuthenticated) {
      _showLoginDialog();
      return;
    }

    final content = _commentController.text.trim();
    if (content.isEmpty) return;

    final commentVm = context.read<CommentViewModel>();

    try {
      if (_isReplying && _replyToCommentId != null) {
        await commentVm.replyToComment(
          widget.momentId,
          _replyToCommentId!,
          content,
        );
      } else {
        await commentVm.addComment(
          widget.momentId,
          content,
        );
      }

      _commentController.clear();
      _cancelReply();

      if (!mounted) return;
      FocusScope.of(context).unfocus();

      await commentVm.loadComments(widget.momentId);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('评论失败: ${e.toString()}')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) {
        if (!didPop) {
          Navigator.of(context).pop(moment);
        }
      },
      child: Scaffold(
        appBar: AppBar(
          title: const Text('动态详情'),
          elevation: 0,
          backgroundColor: Colors.white,
          foregroundColor: Colors.black,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () {
              Navigator.of(context).pop(moment);
            },
          ),
        ),
        bottomNavigationBar: _buildCommentInput(),
        body: Builder(
          builder: (context) {
            if (isLoading && moment == null) {
              return const Center(child: CircularProgressIndicator());
            }
            if (error != null) {
              return Center(child: Text('加载失败: $error'));
            }
            if (moment == null) {
              return const Center(child: Text('动态不存在'));
            }
            return RefreshIndicator(
              onRefresh: _loadMomentDetail,
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                padding: const EdgeInsets.only(bottom: 100),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _buildMomentHeader(theme, colorScheme),
                          const SizedBox(height: 16),
                          if (moment!.typeSpecificData != null)
                            _buildTypeSpecificContent(moment!),
                          if (moment!.content != null &&
                              moment!.content!.isNotEmpty)
                            Text(
                              moment!.content!,
                              style: theme.textTheme.bodyLarge
                                  ?.copyWith(height: 1.5),
                            ),
                          if (moment!.content != null &&
                              moment!.content!.isNotEmpty)
                            const SizedBox(height: 16),
                          if (moment!.images != null &&
                              moment!.images!.isNotEmpty)
                            _buildImageGrid(moment!.images!),
                          if (moment!.images != null &&
                              moment!.images!.isNotEmpty)
                            const SizedBox(height: 16),
                          if (moment!.fishingSpotName != null &&
                              moment!.fishingSpotName!.isNotEmpty)
                            _buildLocationInfo(theme),
                          if (moment!.fishingSpotName != null &&
                              moment!.fishingSpotName!.isNotEmpty)
                            const SizedBox(height: 16),
                          _buildStatsRow(),
                        ],
                      ),
                    ),
                    Divider(
                        color: Colors.grey.shade200, height: 1, thickness: 8),
                    CommentSection(
                      momentId: moment!.id!,
                      onStartReply: _startReply,
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildCommentInput() {
    return Container(
      padding: EdgeInsets.fromLTRB(
          16, 12, 16, 12 + MediaQuery.of(context).viewInsets.bottom),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(top: BorderSide(color: Colors.grey.shade200)),
        boxShadow: [
          // [FIXED] - Replaced .withOpacity(0.05) with .withAlpha(13)
          BoxShadow(color: Colors.black.withAlpha(13), blurRadius: 10)
        ],
      ),
      child: SafeArea(
        top: false,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (_isReplying) ...[
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Text('回复 $_replyToUserName',
                        style: TextStyle(
                            fontSize: 13, color: Colors.blue.shade800)),
                    const Spacer(),
                    GestureDetector(
                      onTap: _cancelReply,
                      child: Icon(Icons.close,
                          size: 16, color: Colors.blue.shade800),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 8),
            ],
            Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _commentController,
                    focusNode: _focusNode,
                    decoration: InputDecoration(
                      hintText:
                          _isReplying ? '回复 $_replyToUserName...' : '写下你的评论...',
                      hintStyle: TextStyle(color: Colors.grey.shade500),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(20),
                        borderSide: BorderSide.none,
                      ),
                      fillColor: Colors.grey.shade100,
                      filled: true,
                      contentPadding: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 10),
                    ),
                    maxLines: null,
                    textInputAction: TextInputAction.send,
                    onSubmitted: (_) => _submitComment(),
                  ),
                ),
                const SizedBox(width: 12),
                GestureDetector(
                  onTap: _submitComment,
                  child: CircleAvatar(
                    radius: 20,
                    backgroundColor: Theme.of(context).primaryColor,
                    child:
                        const Icon(Icons.send, color: Colors.white, size: 20),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMomentHeader(ThemeData theme, ColorScheme colorScheme) {
    return Row(
      children: [
        GestureDetector(
          onTap: () {
            if (moment?.publisher?.id != null) {
              context.push('${AppRoutes.profile}/${moment!.publisher!.id}');
            }
          },
          child: CircleAvatar(
            radius: 24,
            backgroundImage: moment?.publisher?.avatarUrl != null
                ? CachedNetworkImageProvider(moment!.publisher!.avatarUrl!)
                : null,
            child: moment?.publisher?.avatarUrl == null
                ? Icon(Icons.person,
                    size: 24, color: colorScheme.onPrimaryContainer)
                : null,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Text(
                    moment!.publisher?.name ?? moment!.userName ?? '匿名用户',
                    style: theme.textTheme.titleMedium
                        ?.copyWith(fontWeight: FontWeight.bold),
                  ),
                  if (moment!.momentType != null) ...[
                    const SizedBox(width: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 10,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        // [FIXED] - Replaced .withOpacity(0.1) with .withAlpha(26)
                        color: _getMomentTypeColor(moment!.momentType!)
                            .withAlpha(26),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            _getMomentTypeIcon(moment!.momentType!),
                            size: 14,
                            color: _getMomentTypeColor(moment!.momentType!),
                          ),
                          const SizedBox(width: 4),
                          Text(
                            MomentTypeDataParser.getMomentTypeDisplayName(
                                moment!.momentType),
                            style: TextStyle(
                              color: _getMomentTypeColor(moment!.momentType!),
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ],
              ),
              const SizedBox(height: 4),
              Text(
                _formatTime(moment!.createdAt),
                style: theme.textTheme.bodyMedium
                    ?.copyWith(color: Colors.grey.shade600),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildLocationInfo(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(Icons.location_on, size: 16, color: theme.colorScheme.primary),
          const SizedBox(width: 6),
          Text(
            moment!.fishingSpotName!,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade700,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatsRow() {
    return Row(
      children: [
        _buildStatItem(
            icon: Icons.thumb_up_outlined,
            count: moment!.likeCount ?? 0,
            label: '点赞'),
        const SizedBox(width: 24),
        _buildStatItem(
            icon: Icons.comment_outlined,
            count: moment!.commentCount ?? 0,
            label: '评论'),
        const SizedBox(width: 24),
        _buildStatItem(
            icon: Icons.remove_red_eye_outlined, count: 0, label: '浏览'),
      ],
    );
  }

  Widget _buildImageGrid(List<MomentImageVo> images) {
    if (images.isEmpty) return const SizedBox.shrink();

    final imageUrls = images
        .map((image) => image.imageUrl)
        .where((url) => url.isNotEmpty)
        .toList();

    if (imageUrls.isEmpty) return const SizedBox.shrink();

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount:
            imageUrls.length == 1 ? 1 : (imageUrls.length == 2 ? 2 : 3),
        childAspectRatio: 1,
        crossAxisSpacing: 8,
        mainAxisSpacing: 8,
      ),
      itemCount: imageUrls.length,
      itemBuilder: (context, index) {
        return GestureDetector(
          onTap: () {
            // TODO: 实现图片查看器
          },
          child: ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: CachedNetworkImage(
              imageUrl: imageUrls[index],
              fit: BoxFit.cover,
              placeholder: (context, url) =>
                  Container(color: Colors.grey.shade200),
              errorWidget: (context, url, error) => Container(
                  color: Colors.grey.shade200, child: const Icon(Icons.error)),
            ),
          ),
        );
      },
    );
  }

  Widget _buildStatItem(
      {required IconData icon, required int count, required String label}) {
    return Row(
      children: [
        Icon(icon, size: 18, color: Colors.grey.shade600),
        const SizedBox(width: 4),
        Text(count.toString(),
            style: TextStyle(
                color: Colors.grey.shade600, fontWeight: FontWeight.w500)),
        const SizedBox(width: 4),
        Text(label,
            style: TextStyle(color: Colors.grey.shade600, fontSize: 12)),
      ],
    );
  }

  String _formatTime(String? timeString) {
    if (timeString == null) return '未知时间';
    try {
      final time = DateTime.parse(timeString);
      final now = DateTime.now();
      final difference = now.difference(time);
      if (difference.inMinutes < 1) return '刚刚';
      if (difference.inHours < 1) return '${difference.inMinutes}分钟前';
      if (difference.inDays < 1) return '${difference.inHours}小时前';
      if (difference.inDays < 7) return '${difference.inDays}天前';
      return '${time.year}-${time.month.toString().padLeft(2, '0')}-${time.day.toString().padLeft(2, '0')}';
    } catch (e) {
      return timeString;
    }
  }

  Widget _buildTypeSpecificContent(MomentVo moment) {
    switch (moment.momentType) {
      case 'fishing_catch':
        return _buildFishingCatchContent(moment);
      case 'equipment':
        return _buildEquipmentContent(moment);
      case 'technique':
        return _buildTechniqueContent(moment);
      case 'question':
        return _buildQuestionContent(moment);
      default:
        return const SizedBox.shrink();
    }
  }

  Widget _buildFishingCatchContent(MomentVo moment) {
    final catchData =
        MomentTypeDataParser.parseFishingCatch(moment.typeSpecificData);
    if (catchData == null) return const SizedBox.shrink();

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            // [FIXED]
            Colors.blue.shade400.withAlpha(204),
            Colors.blue.shade600.withAlpha(230),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            // [FIXED]
            color: Colors.blue.shade400.withAlpha(77),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Stack(
        children: [
          Positioned(
            right: -20,
            bottom: -20,
            child: Icon(
              Icons.waves,
              size: 80,
              // [FIXED]
              color: Colors.white.withAlpha(26),
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        // [FIXED]
                        color: Colors.white.withAlpha(51),
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: const Icon(
                        Icons.catching_pokemon,
                        size: 20,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(width: 12),
                    const Text(
                      '渔获记录',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    const Spacer(),
                    if (catchData.totalWeight != null)
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 4),
                        decoration: BoxDecoration(
                          // [FIXED]
                          color: Colors.white.withAlpha(230),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Text(
                          '${catchData.totalWeight}kg',
                          style: TextStyle(
                            fontSize: 13,
                            fontWeight: FontWeight.bold,
                            color: Colors.blue.shade700,
                          ),
                        ),
                      ),
                  ],
                ),
                const SizedBox(height: 12),
                if (catchData.caughtFishes != null &&
                    catchData.caughtFishes!.isNotEmpty)
                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children: catchData.caughtFishes!.map((fish) {
                      return Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 6),
                        decoration: BoxDecoration(
                          // [FIXED]
                          color: Colors.white.withAlpha(242),
                          borderRadius: BorderRadius.circular(20),
                          boxShadow: [
                            BoxShadow(
                              // [FIXED]
                              color: Colors.black.withAlpha(26),
                              blurRadius: 4,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.set_meal,
                              size: 16,
                              color: Colors.blue.shade600,
                            ),
                            const SizedBox(width: 6),
                            Text(
                              fish.fishTypeName ?? "未知",
                              style: TextStyle(
                                fontSize: 13,
                                fontWeight: FontWeight.w600,
                                color: Colors.grey.shade800,
                              ),
                            ),
                            const SizedBox(width: 8),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 6, vertical: 2),
                              decoration: BoxDecoration(
                                color: Colors.blue.shade100,
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Text(
                                'x${fish.count ?? 1}',
                                style: TextStyle(
                                  fontSize: 11,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.blue.shade700,
                                ),
                              ),
                            ),
                            if (fish.weight != null) ...[
                              const SizedBox(width: 6),
                              Text(
                                '${fish.weight}kg',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.grey.shade600,
                                ),
                              ),
                            ],
                          ],
                        ),
                      );
                    }).toList(),
                  ),
                if (catchData.fishingMethod != null ||
                    catchData.weatherConditions != null)
                  Padding(
                    padding: const EdgeInsets.only(top: 12),
                    child: Row(
                      children: [
                        if (catchData.fishingMethod != null) ...[
                          Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 10, vertical: 4),
                            decoration: BoxDecoration(
                              // [FIXED]
                              color: Colors.white.withAlpha(51),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                const Icon(
                                  Icons.phishing,
                                  size: 14,
                                  color: Colors.white,
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  catchData.fishingMethod!,
                                  style: const TextStyle(
                                    fontSize: 12,
                                    color: Colors.white,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                        if (catchData.fishingMethod != null &&
                            catchData.weatherConditions != null)
                          const SizedBox(width: 8),
                        if (catchData.weatherConditions != null) ...[
                          Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 10, vertical: 4),
                            decoration: BoxDecoration(
                              // [FIXED]
                              color: Colors.white.withAlpha(51),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                const Icon(
                                  Icons.wb_sunny_outlined,
                                  size: 14,
                                  color: Colors.white,
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  catchData.weatherConditions!,
                                  style: const TextStyle(
                                    fontSize: 12,
                                    color: Colors.white,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEquipmentContent(MomentVo moment) {
    final equipmentData =
        MomentTypeDataParser.parseEquipment(moment.typeSpecificData);
    if (equipmentData == null) return const SizedBox.shrink();

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            // [FIXED]
            Colors.deepOrange.shade400.withAlpha(217),
            Colors.orange.shade600.withAlpha(230),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            // [FIXED]
            color: Colors.orange.shade400.withAlpha(77),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Stack(
        children: [
          Positioned(
            right: -20,
            bottom: -20,
            child: Icon(
              Icons.shopping_bag,
              size: 80,
              // [FIXED]
              color: Colors.white.withAlpha(26),
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        // [FIXED]
                        color: Colors.white.withAlpha(51),
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: const Icon(
                        Icons.shopping_bag,
                        size: 20,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        equipmentData.equipmentName ?? '装备展示',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    if (equipmentData.rating != null)
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 10, vertical: 4),
                        decoration: BoxDecoration(
                          // [FIXED]
                          color: Colors.white.withAlpha(230),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            ...List.generate(5, (index) {
                              return Icon(
                                index < equipmentData.rating!
                                    ? Icons.star
                                    : Icons.star_border,
                                size: 14,
                                color: Colors.amber.shade600,
                              );
                            }),
                          ],
                        ),
                      ),
                  ],
                ),
                const SizedBox(height: 12),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    // [FIXED]
                    color: Colors.white.withAlpha(242),
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        // [FIXED]
                        color: Colors.black.withAlpha(26),
                        blurRadius: 6,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Column(
                    children: [
                      if (equipmentData.category != null ||
                          equipmentData.brand != null)
                        Row(
                          children: [
                            if (equipmentData.category != null)
                              Expanded(
                                child: _buildEquipmentInfoItem(
                                  icon: Icons.category,
                                  label: '类别',
                                  value: equipmentData.category!,
                                  color: Colors.orange.shade600,
                                ),
                              ),
                            if (equipmentData.category != null &&
                                equipmentData.brand != null)
                              Container(
                                width: 1,
                                height: 40,
                                color: Colors.grey.shade200,
                                margin:
                                    const EdgeInsets.symmetric(horizontal: 12),
                              ),
                            if (equipmentData.brand != null)
                              Expanded(
                                child: _buildEquipmentInfoItem(
                                  icon: Icons.business,
                                  label: '品牌',
                                  value: equipmentData.brand!,
                                  color: Colors.orange.shade600,
                                ),
                              ),
                          ],
                        ),
                      if ((equipmentData.category != null ||
                              equipmentData.brand != null) &&
                          (equipmentData.model != null ||
                              equipmentData.price != null))
                        const Divider(height: 20),
                      if (equipmentData.model != null ||
                          equipmentData.price != null)
                        Row(
                          children: [
                            if (equipmentData.model != null)
                              Expanded(
                                child: _buildEquipmentInfoItem(
                                  icon: Icons.model_training,
                                  label: '型号',
                                  value: equipmentData.model!,
                                  color: Colors.orange.shade600,
                                ),
                              ),
                            if (equipmentData.model != null &&
                                equipmentData.price != null)
                              Container(
                                width: 1,
                                height: 40,
                                color: Colors.grey.shade200,
                                margin:
                                    const EdgeInsets.symmetric(horizontal: 12),
                              ),
                            if (equipmentData.price != null)
                              Expanded(
                                child: _buildEquipmentInfoItem(
                                  icon: Icons.attach_money,
                                  label: '价格',
                                  value: equipmentData.price!,
                                  color: Colors.orange.shade600,
                                ),
                              ),
                          ],
                        ),
                    ],
                  ),
                ),
                if (equipmentData.targetFishTypes != null &&
                    equipmentData.targetFishTypes!.isNotEmpty)
                  Padding(
                    padding: const EdgeInsets.only(top: 12),
                    child: Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children: equipmentData.targetFishTypes!.map((fish) {
                        return Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 12, vertical: 6),
                          decoration: BoxDecoration(
                            // [FIXED]
                            color: Colors.white.withAlpha(51),
                            borderRadius: BorderRadius.circular(20),
                            border: Border.all(
                              // [FIXED]
                              color: Colors.white.withAlpha(77),
                              width: 1,
                            ),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                Icons.set_meal,
                                size: 14,
                                // [FIXED]
                                color: Colors.white.withAlpha(230),
                              ),
                              const SizedBox(width: 4),
                              Text(
                                fish.name ?? "未知",
                                style: const TextStyle(
                                  fontSize: 12,
                                  color: Colors.white,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        );
                      }).toList(),
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTechniqueContent(MomentVo moment) {
    final techniqueData =
        MomentTypeDataParser.parseTechnique(moment.typeSpecificData);
    if (techniqueData == null) return const SizedBox.shrink();

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            // [FIXED]
            Colors.green.shade400.withAlpha(217),
            Colors.teal.shade600.withAlpha(230),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            // [FIXED]
            color: Colors.green.shade400.withAlpha(77),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Stack(
        children: [
          Positioned(
            right: -30,
            top: -30,
            child: Container(
              width: 100,
              height: 100,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                // [FIXED]
                color: Colors.white.withAlpha(26),
              ),
            ),
          ),
          Positioned(
            left: -20,
            bottom: -20,
            child: Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                // [FIXED]
                color: Colors.white.withAlpha(20),
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        // [FIXED]
                        color: Colors.white.withAlpha(51),
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: const Icon(
                        Icons.lightbulb,
                        size: 20,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        techniqueData.techniqueName ?? '技巧分享',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    if (techniqueData.difficulty != null)
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 4),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: _getDifficultyGradient(
                                techniqueData.difficulty!),
                          ),
                          borderRadius: BorderRadius.circular(20),
                          boxShadow: [
                            BoxShadow(
                              color:
                                  _getDifficultyColor(techniqueData.difficulty!)
                                      // [FIXED]
                                      .withAlpha(77),
                              blurRadius: 6,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Text(
                          techniqueData.difficulty!,
                          style: const TextStyle(
                            fontSize: 12,
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                  ],
                ),
                if (techniqueData.description != null)
                  Container(
                    margin: const EdgeInsets.only(top: 12),
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      // [FIXED]
                      color: Colors.white.withAlpha(38),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        // [FIXED]
                        color: Colors.white.withAlpha(51),
                        width: 1,
                      ),
                    ),
                    child: Text(
                      techniqueData.description!,
                      style: const TextStyle(
                        fontSize: 13,
                        color: Colors.white,
                        height: 1.4,
                      ),
                    ),
                  ),
                const SizedBox(height: 12),
                Row(
                  children: [
                    if (techniqueData.environments != null &&
                        techniqueData.environments!.isNotEmpty)
                      Expanded(
                        child: Container(
                          padding: const EdgeInsets.all(10),
                          decoration: BoxDecoration(
                            // [FIXED]
                            color: Colors.white.withAlpha(230),
                            borderRadius: BorderRadius.circular(12),
                            boxShadow: [
                              BoxShadow(
                                // [FIXED]
                                color: Colors.black.withAlpha(26),
                                blurRadius: 6,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Icon(
                                    Icons.landscape,
                                    size: 16,
                                    color: Colors.green.shade700,
                                  ),
                                  const SizedBox(width: 6),
                                  Text(
                                    '适用环境',
                                    style: TextStyle(
                                      fontSize: 12,
                                      fontWeight: FontWeight.w600,
                                      color: Colors.green.shade700,
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 6),
                              Wrap(
                                spacing: 4,
                                children:
                                    techniqueData.environments!.map((env) {
                                  return Container(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 8, vertical: 2),
                                    decoration: BoxDecoration(
                                      color: Colors.green.shade50,
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    child: Text(
                                      env,
                                      style: TextStyle(
                                        fontSize: 11,
                                        color: Colors.green.shade800,
                                      ),
                                    ),
                                  );
                                }).toList(),
                              ),
                            ],
                          ),
                        ),
                      ),
                  ],
                ),
                if (techniqueData.targetFishTypes != null &&
                    techniqueData.targetFishTypes!.isNotEmpty)
                  Padding(
                    padding: const EdgeInsets.only(top: 8),
                    child: Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children: techniqueData.targetFishTypes!.map((fish) {
                        return Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 12, vertical: 6),
                          decoration: BoxDecoration(
                            // [FIXED]
                            color: Colors.white.withAlpha(51),
                            borderRadius: BorderRadius.circular(20),
                            border: Border.all(
                              // [FIXED]
                              color: Colors.white.withAlpha(77),
                              width: 1,
                            ),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                Icons.set_meal,
                                size: 14,
                                // [FIXED]
                                color: Colors.white.withAlpha(230),
                              ),
                              const SizedBox(width: 4),
                              Text(
                                fish.name ?? "未知",
                                style: const TextStyle(
                                  fontSize: 12,
                                  color: Colors.white,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        );
                      }).toList(),
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuestionContent(MomentVo moment) {
    final questionData =
        MomentTypeDataParser.parseQuestion(moment.typeSpecificData);
    if (questionData == null) return const SizedBox.shrink();

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            // [FIXED]
            Colors.purple.shade400.withAlpha(217),
            Colors.deepPurple.shade600.withAlpha(230),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            // [FIXED]
            color: Colors.purple.shade400.withAlpha(77),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Stack(
        children: [
          Positioned(
            right: -15,
            top: -15,
            child: Transform.rotate(
              angle: -0.2,
              child: Text(
                '?',
                style: TextStyle(
                  fontSize: 80,
                  fontWeight: FontWeight.bold,
                  // [FIXED]
                  color: Colors.white.withAlpha(26),
                ),
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        // [FIXED]
                        color: Colors.white.withAlpha(51),
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: const Icon(
                        Icons.help_outline,
                        size: 20,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            '问题求助',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.white70,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          const SizedBox(height: 2),
                          Text(
                            questionData.questionTitle ?? '求助问题',
                            style: const TextStyle(
                              fontSize: 15,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                if (questionData.detailedProblem != null)
                  Container(
                    margin: const EdgeInsets.only(top: 12),
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          // [FIXED]
                          Colors.white.withAlpha(242),
                          Colors.white.withAlpha(230),
                        ],
                      ),
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          // [FIXED]
                          color: Colors.black.withAlpha(26),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(
                              Icons.description_outlined,
                              size: 14,
                              color: Colors.purple.shade700,
                            ),
                            const SizedBox(width: 6),
                            Text(
                              '问题描述',
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.w600,
                                color: Colors.purple.shade700,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 6),
                        Text(
                          questionData.detailedProblem!,
                          style: TextStyle(
                            fontSize: 13,
                            color: Colors.grey.shade800,
                            height: 1.4,
                          ),
                        ),
                      ],
                    ),
                  ),
                if (questionData.tags != null && questionData.tags!.isNotEmpty)
                  Padding(
                    padding: const EdgeInsets.only(top: 12),
                    child: Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children: questionData.tags!.map((tag) {
                        return Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 14, vertical: 6),
                          decoration: BoxDecoration(
                            // [FIXED]
                            color: Colors.white.withAlpha(51),
                            borderRadius: BorderRadius.circular(20),
                            border: Border.all(
                              // [FIXED]
                              color: Colors.white.withAlpha(77),
                              width: 1,
                            ),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Container(
                                width: 4,
                                height: 4,
                                decoration: const BoxDecoration(
                                  color: Colors.white,
                                  shape: BoxShape.circle,
                                ),
                              ),
                              const SizedBox(width: 6),
                              Text(
                                tag,
                                style: const TextStyle(
                                  fontSize: 12,
                                  color: Colors.white,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        );
                      }).toList(),
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEquipmentInfoItem({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Column(
      children: [
        Icon(icon, size: 20, color: color),
        const SizedBox(height: 4),
        Text(
          label,
          style: TextStyle(
            fontSize: 11,
            color: Colors.grey.shade600,
          ),
        ),
        const SizedBox(height: 2),
        Text(
          value,
          style: TextStyle(
            fontSize: 13,
            fontWeight: FontWeight.w600,
            color: Colors.grey.shade800,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
      ],
    );
  }

  List<Color> _getDifficultyGradient(String difficulty) {
    switch (difficulty) {
      case '入门级':
        return [Colors.green.shade400, Colors.green.shade600];
      case '进阶级':
        return [Colors.orange.shade400, Colors.orange.shade600];
      case '专家级':
        return [Colors.red.shade400, Colors.red.shade600];
      default:
        return [Colors.grey.shade400, Colors.grey.shade600];
    }
  }

  Color _getDifficultyColor(String difficulty) {
    switch (difficulty) {
      case '入门级':
        return Colors.green;
      case '进阶级':
        return Colors.orange;
      case '专家级':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  Color _getMomentTypeColor(String type) {
    switch (type) {
      case 'fishing_catch':
        return Colors.blue.shade600;
      case 'equipment':
        return Colors.orange.shade600;
      case 'technique':
        return Colors.green.shade600;
      case 'question':
        return Colors.purple.shade600;
      default:
        return Colors.grey;
    }
  }

  IconData _getMomentTypeIcon(String type) {
    switch (type) {
      case 'fishing_catch':
        return Icons.catching_pokemon;
      case 'equipment':
        return Icons.shopping_bag;
      case 'technique':
        return Icons.lightbulb;
      case 'question':
        return Icons.help;
      default:
        return Icons.article;
    }
  }
}
