import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_2d_amap/flutter_2d_amap.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:user_app/config/app_routes.dart';
import 'package:user_app/features/fishing_spots/models/fishing_spot_vo.dart';
import 'package:user_app/features/fishing_spots/screens/create_new_spot_page.dart';
import 'package:user_app/features/fishing_spots/view_models/fishing_spot_view_model.dart';
import 'package:user_app/features/fishing_spots/view_models/weather_card_view_model.dart';
import 'package:user_app/features/fishing_spots/widgets/fishing_spot_details_content.dart';
import 'package:user_app/features/fishing_spots/widgets/spot_moments_section.dart';
import 'package:user_app/features/fishing_spots/widgets/weather_card.dart';
import 'package:user_app/screens/route_planning_page.dart';
import 'package:user_app/utils/date_time_util.dart';
import 'package:user_app/view_models/auth_view_model.dart';

class FishingSpotsPage extends StatefulWidget {
  const FishingSpotsPage({super.key});

  @override
  State<FishingSpotsPage> createState() => _FishingSpotsPageState();
}

class _FishingSpotsPageState extends State<FishingSpotsPage> {
  AMap2DController? _mapController;
  bool _isMapReady = false;
  bool _isMapView = false;
  String _selectedFilter = "全部";
  final List<String> _filterOptions = ["全部", "官方认证", "用户推荐", "免费钓场", "付费钓场"];

  List<String> _selectedFishTypes = [];
  bool _filterHasFacilities = false;
  bool _filterHasParking = false;

  final ScrollController _scrollController = ScrollController();
  late FishingSpotViewModel _spotViewModel;

  @override
  void initState() {
    super.initState();
    _spotViewModel = context.read<FishingSpotViewModel>();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initialLoad();
    });
    _scrollController.addListener(_scrollListener);
  }

  Future<void> _initialLoad() async {
    await Future.wait([
      _spotViewModel.loadFishTypes(),
    ]);
    await _loadData(refresh: true);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_scrollListener);
    _scrollController.dispose();
    _isMapReady = false;
    super.dispose();
  }

  void _scrollListener() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      if (!_spotViewModel.isLoading && _spotViewModel.hasMore) {
        _loadMoreData();
      }
    }
  }

  Future<void> _loadData({bool refresh = false}) async {
    if (_spotViewModel.isLoading && !refresh) return;

    try {
      _applyFiltersToViewModel();
      await _spotViewModel.loadFishingSpots(refresh: refresh);
      _tryAddingMarkersAndCentering();
      if (mounted) setState(() {});
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('加载数据失败: $e'),
            behavior: SnackBarBehavior.floating,
          ),
        );
        setState(() {});
      }
    }
  }

  void _tryAddingMarkersAndCentering() {
    if (_isMapView && _isMapReady && mounted) {
      _addMarkersAndZoom();
    }
  }

  Future<void> _loadMoreData() async {
    if (_spotViewModel.isLoading || !_spotViewModel.hasMore) return;

    try {
      await _spotViewModel.loadMore();
      if (_isMapView && mounted) {
        _addMarkersAndZoom();
      }
      if (mounted) setState(() {});
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('加载更多数据失败: $e'),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  void _addMarkersAndZoom() {
    final controller = _mapController;
    if (controller == null) return;

    controller.clearMarkers().catchError((error) {
      debugPrint("Error clearing markers: $error");
    });

    final spots = _spotViewModel.fishingSpots;
    final validSpots = spots
        .where((spot) => spot.latitude != 0 && spot.longitude != 0)
        .toList();

    if (validSpots.isEmpty) return;

    final markers = validSpots.map((spot) {
      return {
        'latitude': spot.latitude.toString(),
        'longitude': spot.longitude.toString(),
      };
    }).toList();

    controller.addMarkers(markers).then((_) {
      Future.delayed(const Duration(milliseconds: 250), () {
        if (mounted && _mapController != null) {
          _mapController!.zoomToFitMarkers();
        }
      });
    }).catchError((error) {
      debugPrint("Error adding markers: $error");
    });
  }

  void _applyFiltersToViewModel() {
    String? filterTypeParam;
    if (_selectedFilter != "全部") {
      filterTypeParam = _selectedFilter;
    }

    _spotViewModel.applyFilters(
      filter: filterTypeParam,
      fishTypes: _selectedFishTypes.isEmpty ? null : _selectedFishTypes,
      hasFacilities: _filterHasFacilities ? true : null,
      hasParking: _filterHasParking ? true : null,
    );
  }

  @override
  Widget build(BuildContext context) {
    final spotViewModel = context.watch<FishingSpotViewModel>();
    final isLoading = spotViewModel.isLoading;
    final spots = spotViewModel.fishingSpots;
    final hasMore = spotViewModel.hasMore;

    return CustomScrollView(
      controller: _scrollController,
      physics: _isMapView
          ? const NeverScrollableScrollPhysics()
          : const BouncingScrollPhysics(),
      slivers: [
        SliverToBoxAdapter(
          child: Padding(
            padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
            child: _WeatherCardSection(),
          ),
        ),
        SliverToBoxAdapter(
          child: Container(
            color: Colors.white,
            child: Padding(
              padding: const EdgeInsets.fromLTRB(16, 8, 16, 16),
              child: _buildFlatFilterBar(),
            ),
          ),
        ),
        if (isLoading && spots.isEmpty)
          SliverToBoxAdapter(
            child: Container(
              height: MediaQuery.of(context).size.height * 0.5,
              alignment: Alignment.center,
              child: const CircularProgressIndicator(strokeWidth: 2),
            ),
          )
        else if (_isMapView)
          SliverToBoxAdapter(child: _buildFlatMapView())
        else
          _buildFlatSpotsList(spots, isLoading, hasMore),
      ],
    );
  }

  Widget _buildFlatFilterBar() {
    final activeFilters = _getActiveFilterCount();

    return Column(
      children: [
        Row(
          children: [
            Container(
              padding: const EdgeInsets.all(2),
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  _buildViewToggleButton(
                    icon: Icons.list,
                    isSelected: !_isMapView,
                    onTap: () => setState(() => _isMapView = false),
                  ),
                  _buildViewToggleButton(
                    icon: Icons.map_outlined,
                    isSelected: _isMapView,
                    onTap: () {
                      setState(() => _isMapView = true);
                      WidgetsBinding.instance.addPostFrameCallback((_) {
                        _tryAddingMarkersAndCentering();
                      });
                    },
                  ),
                ],
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  children: _filterOptions.map((filter) {
                    final isSelected = _selectedFilter == filter;
                    return Padding(
                      padding: const EdgeInsets.only(right: 8),
                      child: GestureDetector(
                        onTap: () {
                          setState(() => _selectedFilter = filter);
                          _loadData(refresh: true);
                        },
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 8,
                          ),
                          decoration: BoxDecoration(
                            color: isSelected
                                ? Theme.of(context).primaryColor
                                : Colors.transparent,
                            borderRadius: BorderRadius.circular(20),
                            border: Border.all(
                              color: isSelected
                                  ? Theme.of(context).primaryColor
                                  : Colors.grey.shade300,
                            ),
                          ),
                          child: Text(
                            filter,
                            style: TextStyle(
                              color: isSelected
                                  ? Colors.white
                                  : Colors.grey.shade700,
                              fontWeight: FontWeight.w500,
                              fontSize: 14,
                            ),
                          ),
                        ),
                      ),
                    );
                  }).toList(),
                ),
              ),
            ),
            const SizedBox(width: 8),
            GestureDetector(
              onTap: _showFlatFilterDialog,
              child: Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  // [FIXED] - Replaced .withOpacity(0.1) with .withAlpha(26)
                  color: activeFilters > 0
                      ? Theme.of(context).primaryColor.withAlpha(26)
                      : Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(10),
                  border: Border.all(
                    // [FIXED] - Replaced .withOpacity(0.3) with .withAlpha(77)
                    color: activeFilters > 0
                        ? Theme.of(context).primaryColor.withAlpha(77)
                        : Colors.transparent,
                  ),
                ),
                child: Stack(
                  children: [
                    Icon(
                      Icons.tune,
                      size: 20,
                      color: activeFilters > 0
                          ? Theme.of(context).primaryColor
                          : Colors.grey.shade700,
                    ),
                    if (activeFilters > 0)
                      Positioned(
                        top: -2,
                        right: -2,
                        child: Container(
                          width: 8,
                          height: 8,
                          decoration: BoxDecoration(
                            color: Theme.of(context).primaryColor,
                            shape: BoxShape.circle,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ),
            const SizedBox(width: 8),
            GestureDetector(
              onTap: _showAddSpotDialog,
              child: Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: Colors.green.shade50,
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Icon(
                  Icons.add_location_alt,
                  size: 20,
                  color: Colors.green.shade700,
                ),
              ),
            ),
          ],
        ),
        if (activeFilters > 0) ...[
          const SizedBox(height: 12),
          _buildActiveFiltersRow(),
        ],
      ],
    );
  }

  Widget _buildViewToggleButton({
    required IconData icon,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color:
              isSelected ? Theme.of(context).primaryColor : Colors.transparent,
          borderRadius: BorderRadius.circular(6),
        ),
        child: Icon(
          icon,
          size: 20,
          color: isSelected ? Colors.white : Colors.grey.shade600,
        ),
      ),
    );
  }

  Widget _buildActiveFiltersRow() {
    return SizedBox(
      height: 32,
      child: ListView(
        scrollDirection: Axis.horizontal,
        children: [
          if (_selectedFishTypes.isNotEmpty)
            ..._selectedFishTypes.map((fish) => _buildActiveFilterChip(
                  label: fish,
                  onRemove: () {
                    setState(() => _selectedFishTypes.remove(fish));
                    _loadData(refresh: true);
                  },
                )),
          if (_filterHasFacilities)
            _buildActiveFilterChip(
              label: '有设施',
              onRemove: () {
                setState(() => _filterHasFacilities = false);
                _loadData(refresh: true);
              },
            ),
          if (_filterHasParking)
            _buildActiveFilterChip(
              label: '有停车场',
              onRemove: () {
                setState(() => _filterHasParking = false);
                _loadData(refresh: true);
              },
            ),
        ],
      ),
    );
  }

  Widget _buildActiveFilterChip({
    required String label,
    required VoidCallback onRemove,
  }) {
    return Container(
      margin: const EdgeInsets.only(right: 8),
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
      decoration: BoxDecoration(
        // [FIXED] - Replaced .withOpacity(0.1) with .withAlpha(26)
        color: Theme.of(context).primaryColor.withAlpha(26),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            label,
            style: TextStyle(
              color: Theme.of(context).primaryColor,
              fontSize: 13,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(width: 4),
          GestureDetector(
            onTap: onRemove,
            child: Icon(
              Icons.close,
              size: 16,
              color: Theme.of(context).primaryColor,
            ),
          ),
        ],
      ),
    );
  }

  int _getActiveFilterCount() {
    int count = 0;
    if (_selectedFishTypes.isNotEmpty) count += _selectedFishTypes.length;
    if (_filterHasFacilities) count++;
    if (_filterHasParking) count++;
    return count;
  }

  Widget _buildFlatMapView() {
    final spots = context.watch<FishingSpotViewModel>().fishingSpots;

    return Container(
      height: MediaQuery.of(context).size.height * 0.6,
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            // [FIXED] - Replaced .withOpacity(0.05) with .withAlpha(13)
            color: Colors.black.withAlpha(13),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      clipBehavior: Clip.antiAlias,
      child: Stack(children: [
        AMap2DView(
          onAMap2DViewCreated: (controller) async {
            if (!mounted) return;
            _mapController = controller;
            _isMapReady = true;
            _tryAddingMarkersAndCentering();
          },
          onGetLocation: (LngLat location) {},
          onReGeocode: (ReGeocodeResult reGeocode) {},
          onClick: (lat, lon) {
            _handleMapClick(lat, lon);
          },
        ),
        if (spots.isEmpty)
          Center(
            child: Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                // [FIXED] - Replaced .withOpacity(0.8) with .withAlpha(204)
                color: Colors.white.withAlpha(204),
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Text(
                '在当前筛选条件下\n未找到任何钓点',
                textAlign: TextAlign.center,
                style: TextStyle(fontWeight: FontWeight.w500),
              ),
            ),
          ),
      ]),
    );
  }

  void _handleMapClick(num lat, num lon) {
    if (_spotViewModel.fishingSpots.isEmpty) return;

    FishingSpotVo? nearestSpot;
    double minDistance = double.infinity;
    final double latDouble = lat.toDouble();
    final double lonDouble = lon.toDouble();

    for (var spot in _spotViewModel.fishingSpots) {
      final distance = _calculateDistance(
          latDouble, lonDouble, spot.latitude, spot.longitude);

      if (distance < minDistance) {
        minDistance = distance;
        nearestSpot = spot;
      }
    }

    if (nearestSpot != null && minDistance < 0.01) {
      _showSpotDetails(nearestSpot);
    }
  }

  double _calculateDistance(
      double lat1, double lon1, double lat2, double lon2) {
    return ((lat1 - lat2) * (lat1 - lat2) + (lon1 - lon2) * (lon1 - lon2));
  }

  Widget _buildFlatSpotsList(
      List<FishingSpotVo> spots, bool isLoading, bool hasMore) {
    if (spots.isEmpty && !isLoading) {
      return SliverFillRemaining(
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                width: 64,
                height: 64,
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Icon(
                  Icons.water,
                  size: 32,
                  color: Colors.grey.shade400,
                ),
              ),
              const SizedBox(height: 16),
              Text(
                '暂无钓点数据',
                style: TextStyle(
                  color: Colors.grey.shade800,
                  fontSize: 18,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                '调整筛选条件或添加新钓点',
                style: TextStyle(
                  color: Colors.grey.shade500,
                  fontSize: 14,
                ),
              ),
              const SizedBox(height: 24),
              TextButton.icon(
                onPressed: _showAddSpotDialog,
                icon: const Icon(Icons.add),
                label: const Text('添加钓点'),
                style: TextButton.styleFrom(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 12,
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    }

    return SliverList(
      delegate: SliverChildBuilderDelegate(
        (context, index) {
          if (index == spots.length) {
            if (isLoading) {
              return const Padding(
                padding: EdgeInsets.symmetric(vertical: 24),
                child: Center(
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
              );
            } else if (!hasMore) {
              return Padding(
                padding: const EdgeInsets.symmetric(vertical: 24),
                child: Center(
                  child: Text(
                    '已加载全部',
                    style: TextStyle(
                      color: Colors.grey.shade500,
                      fontSize: 13,
                    ),
                  ),
                ),
              );
            }
            return const SizedBox(height: 24);
          }

          if (index >= spots.length) {
            return const SizedBox.shrink();
          }

          return _buildFlatSpotCard(spots[index]);
        },
        childCount: spots.isEmpty ? 0 : spots.length + 1,
      ),
    );
  }

  Widget _buildFlatSpotCard(FishingSpotVo spot) {
    final hasNewMoments = spot.latestMoment != null &&
        spot.latestMoment!.createTime != null &&
        DateTime.now().difference(spot.latestMoment!.createTime!).inHours < 24;

    return Container(
      margin: const EdgeInsets.fromLTRB(16, 0, 16, 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            // [FIXED] - Replaced .withOpacity(0.04) with .withAlpha(10)
            color: Colors.black.withAlpha(10),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: InkWell(
        onTap: () => _showSpotDetails(spot),
        borderRadius: BorderRadius.circular(12),
        child: Column(
          children: [
            _buildFlatImageSection(spot, hasNewMoments),
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          spot.name,
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                            height: 1.2,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      if (spot.rating > 0) ...[
                        const SizedBox(width: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.amber.shade50,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                Icons.star,
                                size: 14,
                                color: Colors.amber.shade700,
                              ),
                              const SizedBox(width: 2),
                              Text(
                                spot.rating.toStringAsFixed(1),
                                style: TextStyle(
                                  color: Colors.amber.shade700,
                                  fontWeight: FontWeight.w600,
                                  fontSize: 13,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ],
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Icon(
                        Icons.place,
                        size: 14,
                        color: Colors.grey.shade500,
                      ),
                      const SizedBox(width: 4),
                      Expanded(
                        child: Text(
                          spot.address,
                          style: TextStyle(
                            fontSize: 13,
                            color: Colors.grey.shade600,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children: [
                      if (spot.isOfficial) _buildFlatTag('官方', Colors.blue),
                      if (spot.verificationLevel > 0)
                        _buildFlatTag('推荐', Colors.green),
                      _buildFlatTag(
                        spot.isPaid ? _getPriceText(spot) : '免费',
                        spot.isPaid ? Colors.orange : Colors.teal,
                      ),
                      if (spot.hasFacilities)
                        _buildFlatTag('设施', Colors.purple),
                    ],
                  ),
                  if (spot.fishTypeList.isNotEmpty ||
                      spot.extraFishTypesList.isNotEmpty) ...[
                    const SizedBox(height: 12),
                    _buildFlatFishTypes(spot),
                  ],
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      _buildFlatStat(Icons.visibility, spot.visitorCount),
                      const SizedBox(width: 16),
                      _buildFlatStat(Icons.check_circle, spot.checkinCount),
                      if (spot.recentMomentsCount > 0) ...[
                        const SizedBox(width: 16),
                        _buildFlatStat(
                            Icons.chat_bubble, spot.recentMomentsCount),
                      ],
                      const Spacer(),
                      TextButton(
                        onPressed: () => _navigateToSpot(spot),
                        style: TextButton.styleFrom(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 8,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            const Icon(Icons.navigation, size: 16),
                            const SizedBox(width: 4),
                            const Text('导航', style: TextStyle(fontSize: 14)),
                          ],
                        ),
                      ),
                    ],
                  ),
                  if (spot.latestMoment != null) ...[
                    const SizedBox(height: 12),
                    GestureDetector(
                      onTap: () => _showSpotMoments(spot),
                      child: Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.grey.shade50,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: Colors.grey.shade200,
                          ),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              Icons.chat_bubble_outline,
                              size: 16,
                              color: Colors.grey.shade600,
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      Text(
                                        '最新动态',
                                        style: TextStyle(
                                          fontSize: 12,
                                          color: Colors.grey.shade600,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                      const SizedBox(width: 8),
                                      Text(
                                        () {
                                          final moment = spot.latestMoment;
                                          final createTime = moment?.createTime;
                                          if (createTime != null) {
                                            return DateTimeUtil.formatTime(
                                                createTime);
                                          }
                                          return '未知时间';
                                        }(),
                                        style: TextStyle(
                                          fontSize: 11,
                                          color: Colors.grey.shade500,
                                        ),
                                      ),
                                    ],
                                  ),
                                  if (spot.latestMoment?.content?.isNotEmpty ==
                                      true) ...[
                                    const SizedBox(height: 4),
                                    Text(
                                      spot.latestMoment?.content ?? '',
                                      style: TextStyle(
                                        fontSize: 13,
                                        color: Colors.grey.shade700,
                                        height: 1.2,
                                      ),
                                      maxLines: 2,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ],
                                ],
                              ),
                            ),
                            Icon(
                              Icons.arrow_forward_ios,
                              size: 12,
                              color: Colors.grey.shade400,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFlatImageSection(FishingSpotVo spot, bool hasNewMoments) {
    return AspectRatio(
      aspectRatio: 16 / 9,
      child: Stack(
        children: [
          Container(
            decoration: BoxDecoration(
              borderRadius: const BorderRadius.vertical(
                top: Radius.circular(12),
              ),
              image: spot.images != null && spot.images!.isNotEmpty
                  ? DecorationImage(
                      image: NetworkImage(spot.images!.first),
                      fit: BoxFit.cover,
                    )
                  : null,
              color: spot.images == null || spot.images!.isEmpty
                  ? Colors.grey.shade200
                  : null,
            ),
            child: spot.images == null || spot.images!.isEmpty
                ? Center(
                    child: Icon(
                      Icons.water,
                      size: 48,
                      color: Colors.grey.shade400,
                    ),
                  )
                : null,
          ),
          if (hasNewMoments)
            Positioned(
              top: 12,
              right: 12,
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 8,
                  vertical: 4,
                ),
                decoration: BoxDecoration(
                  color: Colors.orange,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Text(
                  'NEW',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 10,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildFlatTag(String label, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
      decoration: BoxDecoration(
        // [FIXED] - Replaced .withOpacity(0.1) with .withAlpha(26)
        color: color.withAlpha(26),
        borderRadius: BorderRadius.circular(6),
      ),
      child: Text(
        label,
        style: TextStyle(
          color: color,
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildFlatFishTypes(FishingSpotVo spot) {
    final allFishTypes = [
      ...spot.fishTypeList.map((f) => f.name),
      ...spot.extraFishTypesList,
    ];

    final displayCount = allFishTypes.length > 3 ? 3 : allFishTypes.length;
    final remaining = allFishTypes.length - displayCount;

    return Row(
      children: [
        Icon(
          Icons.pets,
          size: 14,
          color: Colors.grey.shade500,
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            allFishTypes.take(displayCount).join('、') +
                (remaining > 0 ? ' +$remaining' : ''),
            style: TextStyle(
              fontSize: 13,
              color: Colors.grey.shade600,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  Widget _buildFlatStat(IconData icon, int count) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          icon,
          size: 14,
          color: Colors.grey.shade500,
        ),
        const SizedBox(width: 4),
        Text(
          count > 999 ? '999+' : count.toString(),
          style: TextStyle(
            fontSize: 13,
            color: Colors.grey.shade600,
          ),
        ),
      ],
    );
  }

  String _getPriceText(FishingSpotVo spot) {
    if (spot.prices.isNotEmpty) {
      double minPrice = spot.prices
          .map((p) => p.price)
          .reduce((a, b) => a < b ? a : b)
          .toDouble();
      return '¥${minPrice.toStringAsFixed(0)}起';
    } else if (spot.price != null) {
      return '¥${spot.price!.toStringAsFixed(0)}';
    } else {
      return '收费';
    }
  }

  void _showFlatFilterDialog() {
    if (_spotViewModel.availableFishTypesObjects.isEmpty &&
        !_spotViewModel.isLoadingFishTypes) {
      _spotViewModel.loadFishTypes();
    }

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) {
        bool localFilterHasFacilities = _filterHasFacilities;
        bool localFilterHasParking = _filterHasParking;
        List<String> localSelectedFishTypes = List.from(_selectedFishTypes);

        return StatefulBuilder(
          builder: (context, setState) {
            return Container(
              padding: EdgeInsets.only(
                bottom: MediaQuery.of(context).viewInsets.bottom,
              ),
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.vertical(
                  top: Radius.circular(20),
                ),
              ),
              child: SingleChildScrollView(
                child: Padding(
                  padding: const EdgeInsets.all(24),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Center(
                        child: Container(
                          width: 40,
                          height: 4,
                          margin: const EdgeInsets.only(bottom: 20),
                          decoration: BoxDecoration(
                            color: Colors.grey.shade300,
                            borderRadius: BorderRadius.circular(2),
                          ),
                        ),
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text(
                            '筛选钓点',
                            style: TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          TextButton(
                            onPressed: () {
                              setState(() {
                                localSelectedFishTypes = [];
                                localFilterHasFacilities = false;
                                localFilterHasParking = false;
                              });
                            },
                            child: const Text('重置'),
                          ),
                        ],
                      ),
                      const SizedBox(height: 24),
                      _buildFishTypesSection(
                        context,
                        localSelectedFishTypes,
                        (fishTypes) => setState(() {
                          localSelectedFishTypes = fishTypes;
                        }),
                      ),
                      const SizedBox(height: 24),
                      const Text(
                        '场地设施',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 12),
                      _buildFlatCheckbox(
                        '提供基础设施',
                        '餐饮、卫生间等',
                        localFilterHasFacilities,
                        (value) =>
                            setState(() => localFilterHasFacilities = value),
                      ),
                      const SizedBox(height: 8),
                      _buildFlatCheckbox(
                        '有停车场',
                        '提供停车服务',
                        localFilterHasParking,
                        (value) =>
                            setState(() => localFilterHasParking = value),
                      ),
                      const SizedBox(height: 32),
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton(
                          onPressed: () {
                            Navigator.pop(context);
                            this.setState(() {
                              _selectedFishTypes = localSelectedFishTypes;
                              _filterHasFacilities = localFilterHasFacilities;
                              _filterHasParking = localFilterHasParking;
                            });
                            _loadData(refresh: true);
                          },
                          style: ElevatedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            elevation: 0,
                          ),
                          child: const Text(
                            '应用筛选',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildFishTypesSection(
    BuildContext context,
    List<String> selectedFishTypes,
    Function(List<String>) onChanged,
  ) {
    final availableFishTypes = _spotViewModel.availableFishTypesObjects;
    final isLoadingFishTypes = _spotViewModel.isLoadingFishTypes;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              '鱼类品种',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            Row(
              children: [
                if (selectedFishTypes.isNotEmpty)
                  Text(
                    '已选 ${selectedFishTypes.length}',
                    style: TextStyle(
                      fontSize: 13,
                      color: Theme.of(context).primaryColor,
                    ),
                  ),
                const SizedBox(width: 8),
                TextButton(
                  onPressed: availableFishTypes.isEmpty
                      ? null
                      : () {
                          HapticFeedback.lightImpact();
                          if (selectedFishTypes.length ==
                              availableFishTypes.length) {
                            onChanged([]);
                          } else {
                            onChanged(
                                availableFishTypes.map((f) => f.name).toList());
                          }
                        },
                  style: TextButton.styleFrom(
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    minimumSize: Size.zero,
                    tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  ),
                  child: Text(
                    selectedFishTypes.length == availableFishTypes.length
                        ? '清除'
                        : '全选',
                    style: const TextStyle(fontSize: 13),
                  ),
                ),
              ],
            ),
          ],
        ),
        const SizedBox(height: 12),
        if (isLoadingFishTypes)
          const Center(
            child: Padding(
              padding: EdgeInsets.all(20),
              child: CircularProgressIndicator(strokeWidth: 2),
            ),
          )
        else if (availableFishTypes.isEmpty)
          Center(
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                children: [
                  Icon(
                    Icons.pets_outlined,
                    size: 32,
                    color: Colors.grey.shade400,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '暂无鱼类数据',
                    style: TextStyle(
                      color: Colors.grey.shade600,
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),
          )
        else
          Container(
            constraints: BoxConstraints(
              maxHeight: MediaQuery.of(context).size.height * 0.3,
            ),
            child: SingleChildScrollView(
              child: Wrap(
                spacing: 8,
                runSpacing: 8,
                children: availableFishTypes.map((fish) {
                  final isSelected = selectedFishTypes.contains(fish.name);
                  return GestureDetector(
                    onTap: () {
                      HapticFeedback.selectionClick();
                      final newList = List<String>.from(selectedFishTypes);
                      if (isSelected) {
                        newList.remove(fish.name);
                      } else {
                        newList.add(fish.name);
                      }
                      onChanged(newList);
                    },
                    child: AnimatedContainer(
                      duration: const Duration(milliseconds: 200),
                      padding: const EdgeInsets.symmetric(
                        horizontal: 14,
                        vertical: 8,
                      ),
                      decoration: BoxDecoration(
                        // [FIXED] - Replaced .withOpacity(0.1) with .withAlpha(26)
                        color: isSelected
                            ? Theme.of(context).primaryColor.withAlpha(26)
                            : Colors.grey.shade50,
                        borderRadius: BorderRadius.circular(20),
                        border: Border.all(
                          color: isSelected
                              ? Theme.of(context).primaryColor
                              : Colors.grey.shade300,
                          width: isSelected ? 1.5 : 1,
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          if (isSelected) ...[
                            Icon(
                              Icons.check,
                              size: 14,
                              color: Theme.of(context).primaryColor,
                            ),
                            const SizedBox(width: 4),
                          ],
                          Text(
                            fish.name,
                            style: TextStyle(
                              color: isSelected
                                  ? Theme.of(context).primaryColor
                                  : Colors.grey.shade700,
                              fontWeight: isSelected
                                  ? FontWeight.w600
                                  : FontWeight.w500,
                              fontSize: 13,
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                }).toList(),
              ),
            ),
          ),
        if (selectedFishTypes.isNotEmpty) ...[
          const SizedBox(height: 12),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              // [FIXED] - Replaced .withOpacity(0.05) and .withOpacity(0.2)
              color: Theme.of(context).primaryColor.withAlpha(13),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: Theme.of(context).primaryColor.withAlpha(51),
              ),
            ),
            child: Text(
              '已选择: ${selectedFishTypes.join('、')}',
              style: TextStyle(
                fontSize: 12,
                color: Theme.of(context).primaryColor,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildFlatCheckbox(
    String title,
    String subtitle,
    bool value,
    ValueChanged<bool> onChanged,
  ) {
    return GestureDetector(
      onTap: () {
        HapticFeedback.selectionClick();
        onChanged(!value);
      },
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          // [FIXED] - Replaced .withOpacity(0.05) and .withOpacity(0.3)
          color: value
              ? Theme.of(context).primaryColor.withAlpha(13)
              : Colors.grey.shade50,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: value
                ? Theme.of(context).primaryColor.withAlpha(77)
                : Colors.grey.shade200,
          ),
        ),
        child: Row(
          children: [
            Container(
              width: 20,
              height: 20,
              decoration: BoxDecoration(
                color: value ? Theme.of(context).primaryColor : Colors.white,
                borderRadius: BorderRadius.circular(4),
                border: Border.all(
                  color: value
                      ? Theme.of(context).primaryColor
                      : Colors.grey.shade400,
                ),
              ),
              child: value
                  ? const Icon(
                      Icons.check,
                      size: 14,
                      color: Colors.white,
                    )
                  : null,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: value
                          ? Theme.of(context).primaryColor
                          : Colors.grey.shade800,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _navigateToSpot(FishingSpotVo spot) {
    if (spot.latitude != 0 && spot.longitude != 0) {
      final lngLat = LngLat(spot.longitude, spot.latitude);
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => RoutePlanningPage(
            address: spot.address,
            destination: lngLat,
          ),
        ),
      );
    }
  }

  void _showSpotDetails(FishingSpotVo spot) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return DraggableScrollableSheet(
          initialChildSize: 0.6,
          maxChildSize: 0.95,
          minChildSize: 0.4,
          builder: (context, scrollController) {
            return Container(
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.vertical(
                  top: Radius.circular(20),
                ),
              ),
              padding: const EdgeInsets.all(16),
              child: FishingSpotDetailsContent(
                spot: spot,
                scrollController: scrollController,
                onNavigationPressed: () {
                  _navigateToSpot(spot);
                },
                onCheckinPressed: () {
                  _checkinSpot(spot.id);
                },
                onViewDynamicPressed: () {
                  _showSpotMoments(spot);
                },
              ),
            );
          },
        );
      },
    );
  }

  void _showAddSpotDialog() {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: const Text(
            '添加新钓点',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.w600,
            ),
          ),
          content: const Text(
            '您可以通过以下方式添加钓点：\n\n'
            '1. 发布带有位置信息的钓获动态\n'
            '2. 提交钓点信息（需要审核）\n\n'
            '要继续操作吗？',
            style: TextStyle(height: 1.5),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('取消'),
            ),
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                if (!isAuthenticated()) {
                  _showLoginDialog('发布动态');
                  return;
                }
                context.push(AppRoutes.publishMoment);
              },
              child: const Text('发布动态'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                if (!isAuthenticated()) {
                  _showLoginDialog('提交钓点');
                  return;
                }
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => CreateNewSpotPage(
                      onSpotCreated: (newSpot) {
                        _loadData(refresh: true);
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('钓点已成功添加'),
                            behavior: SnackBarBehavior.floating,
                          ),
                        );
                      },
                    ),
                  ),
                );
              },
              style: ElevatedButton.styleFrom(
                elevation: 0,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text('提交钓点'),
            ),
          ],
        );
      },
    );
  }

  void _showLoginDialog(String action) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: const Text('需要登录'),
          content: Text('您需要先登录才能$action'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('取消'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                context.push(AppRoutes.login);
              },
              style: ElevatedButton.styleFrom(
                elevation: 0,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text('去登录'),
            ),
          ],
        );
      },
    );
  }

  Future<void> _checkinSpot(int spotId) async {
    if (!isAuthenticated()) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('请先登录再签到'),
            behavior: SnackBarBehavior.floating,
            action: SnackBarAction(
              label: '去登录',
              onPressed: () {
                context.push(AppRoutes.login);
              },
            ),
          ),
        );
      }
      return;
    }

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) {
        return const Center(
          child: CircularProgressIndicator(strokeWidth: 2),
        );
      },
    );

    try {
      final success = await _spotViewModel.checkinFishingSpot(spotId);
      if (context.mounted) {
        Navigator.pop(context);
      }

      if (success && mounted) {
        await _spotViewModel.loadFishingSpots(refresh: true);
        final updatedSpot = _spotViewModel.fishingSpots.firstWhere(
          (element) => element.id == spotId,
        );

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('签到成功！已有${updatedSpot.checkinCount}人签到'),
              behavior: SnackBarBehavior.floating,
              backgroundColor: Colors.green,
            ),
          );
        }
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('签到失败，请稍后再试'),
            behavior: SnackBarBehavior.floating,
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        Navigator.pop(context);
      }
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('签到出错: $e'),
            behavior: SnackBarBehavior.floating,
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showSpotMoments(FishingSpotVo spot) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return DraggableScrollableSheet(
          initialChildSize: 0.95,
          maxChildSize: 0.95,
          minChildSize: 0.5,
          builder: (context, scrollController) {
            return SpotMomentsSection(
              fishingSpotId: spot.id,
              fishingSpotName: spot.name,
            );
          },
        );
      },
    );
  }

  bool isAuthenticated() {
    final authViewModel = Provider.of<AuthViewModel>(context, listen: false);
    return authViewModel.isUserLoggedIn();
  }
}

class _WeatherCardSection extends StatefulWidget {
  @override
  State<_WeatherCardSection> createState() => _WeatherCardSectionState();
}

class _WeatherCardSectionState extends State<_WeatherCardSection> {
  late WeatherCardViewModel _weatherViewModel;

  @override
  void initState() {
    super.initState();
    _weatherViewModel = context.read<WeatherCardViewModel>();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _weatherViewModel.fetchWeatherData();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<WeatherCardViewModel>(
      builder: (context, weatherViewModel, _) {
        return WeatherCard(
          isLoading: weatherViewModel.isLoading,
          weatherData: weatherViewModel.weatherData,
          fishingRecommendation: weatherViewModel.fishingRecommendationData,
        );
      },
    );
  }
}
