enum SearchResultType {
  moment,
  fishingSpot,
  user,
}

class SearchResult {
  final String id;
  final SearchResultType type;
  final String title;
  final String? subtitle;
  final String? imageUrl;
  final Map<String, dynamic> data;

  SearchResult({
    required this.id,
    required this.type,
    required this.title,
    this.subtitle,
    this.imageUrl,
    required this.data,
  });

  factory SearchResult.fromMoment(Map<String, dynamic> moment) {
    return SearchResult(
      id: moment['id'].toString(),
      type: SearchResultType.moment,
      title: moment['content'] ?? '动态内容',
      subtitle: '${moment['publisher']?['name'] ?? '匿名用户'} • ${_formatTime(moment['createdAt'])}',
      imageUrl: moment['images']?.isNotEmpty == true 
          ? moment['images'][0]['imageUrl'] ?? moment['images'][0]
          : null,
      data: moment,
    );
  }

  factory SearchResult.fromFishingSpot(Map<String, dynamic> spot) {
    return SearchResult(
      id: spot['id'].toString(),
      type: SearchResultType.fishingSpot,
      title: spot['name'] ?? '钓点',
      subtitle: spot['address'] ?? spot['description'],
      imageUrl: spot['images']?.isNotEmpty == true 
          ? spot['images'][0]['imageUrl'] ?? spot['images'][0]
          : null,
      data: spot,
    );
  }

  factory SearchResult.fromUser(Map<String, dynamic> user) {
    return SearchResult(
      id: user['id'].toString(),
      type: SearchResultType.user,
      title: user['name'] ?? user['username'] ?? '用户',
      subtitle: user['bio'] ?? '钓鱼爱好者',
      imageUrl: user['avatarUrl'],
      data: user,
    );
  }

  static String _formatTime(dynamic time) {
    if (time == null) return '';
    
    try {
      DateTime dateTime;
      if (time is String) {
        dateTime = DateTime.parse(time);
      } else if (time is DateTime) {
        dateTime = time;
      } else {
        return '';
      }

      final now = DateTime.now();
      final difference = now.difference(dateTime);

      if (difference.inDays > 0) {
        return '${difference.inDays}天前';
      } else if (difference.inHours > 0) {
        return '${difference.inHours}小时前';
      } else if (difference.inMinutes > 0) {
        return '${difference.inMinutes}分钟前';
      } else {
        return '刚刚';
      }
    } catch (e) {
      return '';
    }
  }
}
