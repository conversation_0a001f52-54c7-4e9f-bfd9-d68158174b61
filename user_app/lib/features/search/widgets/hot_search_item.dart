import 'package:flutter/material.dart';

class HotSearchItem extends StatelessWidget {
  final String query;
  final int index;
  final VoidCallback onTap;

  const HotSearchItem({
    super.key,
    required this.query,
    required this.index,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final isTop3 = index <= 3;
    
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: isTop3 
              ? Theme.of(context).primaryColor.withOpacity(0.1)
              : Colors.grey.shade100,
          borderRadius: BorderRadius.circular(20),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 20,
              height: 20,
              decoration: BoxDecoration(
                color: isTop3 
                    ? Theme.of(context).primaryColor
                    : Colors.grey.shade400,
                borderRadius: BorderRadius.circular(10),
              ),
              child: Center(
                child: Text(
                  index.toString(),
                  style: TextStyle(
                    fontSize: 11,
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
            const SizedBox(width: 8),
            Text(
              query,
              style: TextStyle(
                fontSize: 13,
                color: isTop3 
                    ? Theme.of(context).primaryColor
                    : Colors.grey.shade700,
                fontWeight: isTop3 ? FontWeight.w600 : FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
