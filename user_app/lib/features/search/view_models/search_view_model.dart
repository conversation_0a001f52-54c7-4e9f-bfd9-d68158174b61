import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:user_app/features/search/models/search_result.dart';
import 'package:user_app/features/search/services/search_service.dart';

class SearchViewModel extends ChangeNotifier {
  final SearchService _searchService;
  
  SearchViewModel(this._searchService);

  // 搜索状态
  bool _isLoading = false;
  String _currentQuery = '';
  List<SearchResult> _results = [];
  List<String> _suggestions = [];
  
  // 搜索历史和热门搜索
  List<String> _searchHistory = [];
  List<String> _hotSearches = [];

  // Getters
  bool get isLoading => _isLoading;
  String get currentQuery => _currentQuery;
  List<SearchResult> get results => _results;
  List<String> get suggestions => _suggestions;
  List<String> get searchHistory => _searchHistory;
  List<String> get hotSearches => _hotSearches;
  
  bool get hasResults => _results.isNotEmpty;
  bool get hasSuggestions => _suggestions.isNotEmpty;

  // 执行搜索
  Future<void> search(String query) async {
    if (query.trim().isEmpty) return;
    
    _isLoading = true;
    _currentQuery = query;
    _suggestions.clear();
    notifyListeners();

    try {
      _results = await _searchService.search(query);
    } catch (e) {
      debugPrint('Search error: $e');
      _results = [];
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // 搜索建议
  Future<void> searchSuggestions(String query) async {
    if (query.trim().isEmpty) {
      _suggestions.clear();
      notifyListeners();
      return;
    }

    try {
      _suggestions = await _searchService.getSuggestions(query);
      notifyListeners();
    } catch (e) {
      debugPrint('Search suggestions error: $e');
      _suggestions = [];
      notifyListeners();
    }
  }

  // 清空搜索结果
  void clearResults() {
    _results.clear();
    _suggestions.clear();
    _currentQuery = '';
    notifyListeners();
  }

  // 搜索历史管理
  Future<void> loadSearchHistory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _searchHistory = prefs.getStringList('search_history') ?? [];
      notifyListeners();
    } catch (e) {
      debugPrint('Load search history error: $e');
    }
  }

  Future<void> addToHistory(String query) async {
    if (query.trim().isEmpty) return;
    
    try {
      // 移除重复项
      _searchHistory.remove(query);
      // 添加到开头
      _searchHistory.insert(0, query);
      // 限制历史记录数量
      if (_searchHistory.length > 10) {
        _searchHistory = _searchHistory.take(10).toList();
      }
      
      final prefs = await SharedPreferences.getInstance();
      await prefs.setStringList('search_history', _searchHistory);
      notifyListeners();
    } catch (e) {
      debugPrint('Add to search history error: $e');
    }
  }

  Future<void> removeFromHistory(String query) async {
    try {
      _searchHistory.remove(query);
      final prefs = await SharedPreferences.getInstance();
      await prefs.setStringList('search_history', _searchHistory);
      notifyListeners();
    } catch (e) {
      debugPrint('Remove from search history error: $e');
    }
  }

  Future<void> clearHistory() async {
    try {
      _searchHistory.clear();
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('search_history');
      notifyListeners();
    } catch (e) {
      debugPrint('Clear search history error: $e');
    }
  }

  // 热门搜索
  Future<void> loadHotSearches() async {
    try {
      _hotSearches = await _searchService.getHotSearches();
      notifyListeners();
    } catch (e) {
      debugPrint('Load hot searches error: $e');
      // 使用默认热门搜索
      _hotSearches = [
        '鲫鱼',
        '路亚',
        '黑坑',
        '野钓',
        '台钓',
        '饵料',
        '钓点',
        '技巧',
      ];
      notifyListeners();
    }
  }
}
