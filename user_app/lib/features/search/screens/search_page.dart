import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:user_app/features/search/view_models/search_view_model.dart';
import 'package:user_app/features/search/widgets/search_result_item.dart';
import 'package:user_app/features/search/widgets/search_history_item.dart';
import 'package:user_app/features/search/widgets/hot_search_item.dart';

class SearchPage extends StatefulWidget {
  const SearchPage({super.key});

  @override
  State<SearchPage> createState() => _SearchPageState();
}

class _SearchPageState extends State<SearchPage> {
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  late SearchViewModel _searchViewModel;

  @override
  void initState() {
    super.initState();
    _searchViewModel = context.read<SearchViewModel>();
    _focusNode.requestFocus();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _searchViewModel.loadSearchHistory();
      _searchViewModel.loadHotSearches();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  void _performSearch(String query) {
    if (query.trim().isEmpty) return;

    _searchViewModel.search(query.trim());
    _searchViewModel.addToHistory(query.trim());
    _focusNode.unfocus();
  }

  void _clearSearch() {
    _searchController.clear();
    _searchViewModel.clearResults();
    _focusNode.requestFocus();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          onPressed: () => Navigator.pop(context),
          icon: Icon(Icons.arrow_back, color: Colors.grey.shade700),
        ),
        title: Container(
          height: 40,
          decoration: BoxDecoration(
            color: Colors.grey.shade100,
            borderRadius: BorderRadius.circular(20),
          ),
          child: TextField(
            controller: _searchController,
            focusNode: _focusNode,
            decoration: InputDecoration(
              hintText: '搜索动态、钓点、用户...',
              hintStyle: TextStyle(color: Colors.grey.shade500, fontSize: 14),
              prefixIcon:
                  Icon(Icons.search, color: Colors.grey.shade500, size: 20),
              suffixIcon: _searchController.text.isNotEmpty
                  ? IconButton(
                      onPressed: _clearSearch,
                      icon: Icon(Icons.clear,
                          color: Colors.grey.shade500, size: 20),
                    )
                  : null,
              border: InputBorder.none,
              contentPadding:
                  const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            ),
            onChanged: (value) {
              setState(() {});
              if (value.trim().isNotEmpty) {
                _searchViewModel.searchSuggestions(value.trim());
              }
            },
            onSubmitted: _performSearch,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () {
              if (_searchController.text.trim().isNotEmpty) {
                _performSearch(_searchController.text.trim());
              }
            },
            child: Text(
              '搜索',
              style: TextStyle(
                color: Theme.of(context).primaryColor,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          const SizedBox(width: 8),
        ],
      ),
      body: Consumer<SearchViewModel>(
        builder: (context, searchViewModel, child) {
          if (searchViewModel.hasResults) {
            return _buildSearchResults(searchViewModel);
          } else if (searchViewModel.hasSuggestions) {
            return _buildSearchSuggestions(searchViewModel);
          } else {
            return _buildSearchHome(searchViewModel);
          }
        },
      ),
    );
  }

  Widget _buildSearchHome(SearchViewModel searchViewModel) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 搜索历史
          if (searchViewModel.searchHistory.isNotEmpty) ...[
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '搜索历史',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.grey.shade800,
                  ),
                ),
                TextButton(
                  onPressed: () {
                    searchViewModel.clearHistory();
                  },
                  child: Text(
                    '清空',
                    style: TextStyle(
                      color: Colors.grey.shade600,
                      fontSize: 14,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: searchViewModel.searchHistory
                  .map((query) => SearchHistoryItem(
                        query: query,
                        onTap: () {
                          _searchController.text = query;
                          _performSearch(query);
                        },
                        onDelete: () {
                          searchViewModel.removeFromHistory(query);
                        },
                      ))
                  .toList(),
            ),
            const SizedBox(height: 24),
          ],

          // 热门搜索
          if (searchViewModel.hotSearches.isNotEmpty) ...[
            Text(
              '热门搜索',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.grey.shade800,
              ),
            ),
            const SizedBox(height: 12),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: searchViewModel.hotSearches
                  .asMap()
                  .entries
                  .map((entry) => HotSearchItem(
                        query: entry.value,
                        index: entry.key + 1,
                        onTap: () {
                          _searchController.text = entry.value;
                          _performSearch(entry.value);
                        },
                      ))
                  .toList(),
            ),
            const SizedBox(height: 24),
          ],

          // 搜索建议
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '搜索建议',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Colors.grey.shade700,
                  ),
                ),
                const SizedBox(height: 12),
                Text(
                  '• 尝试搜索钓点名称、地址或描述\n'
                  '• 搜索用户昵称或用户ID\n'
                  '• 搜索动态内容或标签\n'
                  '• 使用关键词如"鲫鱼"、"路亚"、"黑坑"等',
                  style: TextStyle(
                    fontSize: 13,
                    color: Colors.grey.shade600,
                    height: 1.5,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchSuggestions(SearchViewModel searchViewModel) {
    return Container(
      color: Colors.white,
      child: ListView.builder(
        itemCount: searchViewModel.suggestions.length,
        itemBuilder: (context, index) {
          final suggestion = searchViewModel.suggestions[index];
          return ListTile(
            leading: Icon(Icons.search, color: Colors.grey.shade500),
            title: Text(suggestion),
            onTap: () {
              _searchController.text = suggestion;
              _performSearch(suggestion);
            },
          );
        },
      ),
    );
  }

  Widget _buildSearchResults(SearchViewModel searchViewModel) {
    if (searchViewModel.isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (searchViewModel.results.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 80,
              color: Colors.grey.shade300,
            ),
            const SizedBox(height: 16),
            Text(
              '没有找到相关结果',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey.shade600,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '尝试更换关键词或使用其他搜索条件',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade500,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: searchViewModel.results.length,
      itemBuilder: (context, index) {
        final result = searchViewModel.results[index];
        return SearchResultItem(
          result: result,
          query: searchViewModel.currentQuery,
        );
      },
    );
  }
}
