import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:user_app/features/fishing_spots/utils/moment_type_data_parser.dart';
import 'package:user_app/features/moments/screens/moment_detail_page.dart';
import 'package:user_app/models/moment/moment_vo.dart';
import 'package:user_app/utils/date_time_util.dart';

typedef MomentBuilder = Widget Function(BuildContext context, MomentVo moment);
typedef OnMomentTap = void Function(MomentVo moment);
typedef OnUserTap = void Function(dynamic user);
typedef OnLikeTap = void Function(int momentId);
typedef OnReportTap = void Function(MomentVo moment);

class MomentsList extends StatefulWidget {
  final List<MomentVo> moments;
  final bool isLoading;
  final bool hasMore;
  final ScrollController? scrollController;
  final Future<void> Function() onRefresh;
  final VoidCallback? onLoadMore;

  // 自定义回调
  final OnMomentTap? onMomentTap;
  final OnUserTap? onUserTap;
  final OnLikeTap? onLikeTap;
  final OnReportTap? onReportTap;

  // 自定义构建器（如果需要完全自定义卡片）
  final MomentBuilder? momentBuilder;

  // 显示选项
  final bool showSpotInfo;
  final bool showFloatingActionButton;
  final Widget? emptyWidget;
  final EdgeInsets padding;

  const MomentsList({
    super.key,
    required this.moments,
    required this.isLoading,
    required this.hasMore,
    required this.onRefresh,
    this.scrollController,
    this.onLoadMore,
    this.onMomentTap,
    this.onUserTap,
    this.onLikeTap,
    this.onReportTap,
    this.momentBuilder,
    this.showSpotInfo = true,
    this.showFloatingActionButton = false,
    this.emptyWidget,
    this.padding = const EdgeInsets.fromLTRB(16, 12, 16, 16),
  });

  @override
  State<MomentsList> createState() => _MomentsListState();
}

class _MomentsListState extends State<MomentsList> {
  late ScrollController _scrollController;

  @override
  void initState() {
    super.initState();
    _scrollController = widget.scrollController ?? ScrollController();
    if (widget.onLoadMore != null) {
      _scrollController.addListener(_onScroll);
    }
  }

  @override
  void dispose() {
    if (widget.scrollController == null) {
      _scrollController.dispose();
    }
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 400) {
      if (!widget.isLoading && widget.hasMore && widget.onLoadMore != null) {
        widget.onLoadMore!();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (widget.isLoading && widget.moments.isEmpty) {
      return const Center(
        child: Padding(
          padding: EdgeInsets.all(32),
          child: CircularProgressIndicator(),
        ),
      );
    }

    if (widget.moments.isEmpty) {
      return widget.emptyWidget ?? _buildDefaultEmptyWidget();
    }

    return RefreshIndicator(
      onRefresh: widget.onRefresh,
      child: ListView.builder(
        controller: _scrollController,
        padding: widget.padding,
        itemCount: widget.moments.length +
            (widget.isLoading && widget.moments.isNotEmpty ? 1 : 0) +
            (!widget.hasMore && widget.moments.isNotEmpty ? 1 : 0),
        itemBuilder: (context, index) {
          if (index < widget.moments.length) {
            final moment = widget.moments[index];
            return widget.momentBuilder?.call(context, moment) ??
                _buildDefaultMomentCard(moment);
          } else if (widget.isLoading && widget.moments.isNotEmpty) {
            return _buildLoadingIndicator();
          } else {
            return _buildNoMoreDataIndicator();
          }
        },
      ),
    );
  }

  Widget _buildDefaultEmptyWidget() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.circular(16),
              ),
              child: Icon(
                Icons.article_outlined,
                size: 32,
                color: Colors.grey.shade400,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              '暂无动态数据',
              style: TextStyle(
                color: Colors.grey.shade800,
                fontSize: 18,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '还没有人分享动态',
              style: TextStyle(
                color: Colors.grey.shade500,
                fontSize: 14,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDefaultMomentCard(MomentVo moment) {
    return GestureDetector(
      onTap: () {
        if (widget.onMomentTap != null) {
          widget.onMomentTap!(moment);
        } else {
          _defaultMomentTap(moment);
        }
      },
      child: Container(
        margin: const EdgeInsets.only(bottom: 12),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.04),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 用户信息栏
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 16, 16, 0),
              child: Row(
                children: [
                  GestureDetector(
                    onTap: () {
                      if (widget.onUserTap != null &&
                          moment.publisher != null) {
                        widget.onUserTap!(moment.publisher);
                      }
                    },
                    child: CircleAvatar(
                      radius: 20,
                      backgroundImage: moment.publisher?.avatarUrl != null
                          ? CachedNetworkImageProvider(
                              moment.publisher!.avatarUrl!)
                          : null,
                      child: moment.publisher?.avatarUrl == null
                          ? const Icon(Icons.person, size: 20)
                          : null,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          moment.publisher?.name ?? '匿名用户',
                          style: const TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: 15,
                          ),
                        ),
                        if (widget.showSpotInfo &&
                            moment.fishingSpotName != null)
                          Text(
                            moment.fishingSpotName!,
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey.shade600,
                            ),
                          ),
                      ],
                    ),
                  ),
                  if (moment.momentType != null)
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 10,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: _getMomentTypeColor(moment.momentType!)
                            .withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            _getMomentTypeIcon(moment.momentType!),
                            size: 14,
                            color: _getMomentTypeColor(moment.momentType!),
                          ),
                          const SizedBox(width: 4),
                          Text(
                            MomentTypeDataParser.getMomentTypeDisplayName(
                                moment.momentType),
                            style: TextStyle(
                              color: _getMomentTypeColor(moment.momentType!),
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                ],
              ),
            ),

            // 根据动态类型展示特定数据
            if (moment.typeSpecificData != null)
              _buildTypeSpecificContent(moment),

            // 内容
            if (moment.content != null && moment.content!.isNotEmpty)
              Padding(
                padding: const EdgeInsets.fromLTRB(16, 12, 16, 0),
                child: Text(
                  moment.content!,
                  style: const TextStyle(
                    fontSize: 15,
                    height: 1.4,
                    color: Colors.black87,
                  ),
                  maxLines: 3,
                  overflow: TextOverflow.ellipsis,
                ),
              ),

            // 图片
            if (moment.images != null && moment.images!.isNotEmpty)
              _buildImageGrid(moment.images!),

            // 互动栏
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 12, 16, 16),
              child: Row(
                children: [
                  _buildInteractionButton(
                    icon: (moment.isLiked ?? false)
                        ? Icons.thumb_up
                        : Icons.thumb_up_outlined,
                    count: moment.likeCount,
                    color: (moment.isLiked ?? false)
                        ? Theme.of(context).primaryColor
                        : null,
                    onTap: widget.onLikeTap != null && moment.id != null
                        ? () => widget.onLikeTap!(moment.id!)
                        : null,
                  ),
                  const SizedBox(width: 16),
                  _buildInteractionButton(
                    icon: Icons.chat_bubble_outline,
                    count: moment.commentCount,
                    onTap: () => widget.onMomentTap != null
                        ? widget.onMomentTap!(moment)
                        : _defaultMomentTap(moment),
                  ),
                  const Spacer(),
                  Text(
                    _formatTimeAgo(moment.createdAt),
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey.shade500,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 构建类型特定内容
  Widget _buildTypeSpecificContent(MomentVo moment) {
    switch (moment.momentType) {
      case 'fishing_catch':
        return _buildFishingCatchContent(moment);
      case 'equipment':
        return _buildEquipmentContent(moment);
      case 'technique':
        return _buildTechniqueContent(moment);
      case 'question':
        return _buildQuestionContent(moment);
      default:
        return const SizedBox.shrink();
    }
  }

  // 构建钓获分享内容
  Widget _buildFishingCatchContent(MomentVo moment) {
    final catchData =
        MomentTypeDataParser.parseFishingCatch(moment.typeSpecificData);
    if (catchData == null) return const SizedBox.shrink();

    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 12, 16, 0),
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.blue.shade400.withOpacity(0.8),
              Colors.blue.shade600.withOpacity(0.9),
            ],
          ),
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.blue.shade400.withOpacity(0.3),
              blurRadius: 12,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Stack(
          children: [
            // 装饰性背景
            Positioned(
              right: -20,
              bottom: -20,
              child: Icon(
                Icons.waves,
                size: 80,
                color: Colors.white.withOpacity(0.1),
              ),
            ),
            // 内容
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: const Icon(
                          Icons.catching_pokemon,
                          size: 20,
                          color: Colors.white,
                        ),
                      ),
                      const SizedBox(width: 12),
                      const Text(
                        '渔获记录',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                      const Spacer(),
                      if (catchData.totalWeight != null)
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 12, vertical: 4),
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.9),
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: Text(
                            '${catchData.totalWeight}kg',
                            style: TextStyle(
                              fontSize: 13,
                              fontWeight: FontWeight.bold,
                              color: Colors.blue.shade700,
                            ),
                          ),
                        ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  if (catchData.caughtFishes != null &&
                      catchData.caughtFishes!.isNotEmpty)
                    Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children: catchData.caughtFishes!.map((fish) {
                        return Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 12, vertical: 6),
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.95),
                            borderRadius: BorderRadius.circular(20),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.1),
                                blurRadius: 4,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                Icons.set_meal,
                                size: 16,
                                color: Colors.blue.shade600,
                              ),
                              const SizedBox(width: 6),
                              Text(
                                '${fish.fishTypeName ?? "未知"}',
                                style: TextStyle(
                                  fontSize: 13,
                                  fontWeight: FontWeight.w600,
                                  color: Colors.grey.shade800,
                                ),
                              ),
                              const SizedBox(width: 8),
                              Container(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 6, vertical: 2),
                                decoration: BoxDecoration(
                                  color: Colors.blue.shade100,
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Text(
                                  'x${fish.count ?? 1}',
                                  style: TextStyle(
                                    fontSize: 11,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.blue.shade700,
                                  ),
                                ),
                              ),
                              if (fish.weight != null) ...[
                                const SizedBox(width: 6),
                                Text(
                                  '${fish.weight}kg',
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Colors.grey.shade600,
                                  ),
                                ),
                              ],
                            ],
                          ),
                        );
                      }).toList(),
                    ),
                  if (catchData.fishingMethod != null ||
                      catchData.weatherConditions != null)
                    Padding(
                      padding: const EdgeInsets.only(top: 12),
                      child: Row(
                        children: [
                          if (catchData.fishingMethod != null) ...[
                            Container(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 10, vertical: 4),
                              decoration: BoxDecoration(
                                color: Colors.white.withOpacity(0.2),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  const Icon(
                                    Icons.phishing,
                                    size: 14,
                                    color: Colors.white,
                                  ),
                                  const SizedBox(width: 4),
                                  Text(
                                    catchData.fishingMethod!,
                                    style: const TextStyle(
                                      fontSize: 12,
                                      color: Colors.white,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                          if (catchData.fishingMethod != null &&
                              catchData.weatherConditions != null)
                            const SizedBox(width: 8),
                          if (catchData.weatherConditions != null) ...[
                            Container(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 10, vertical: 4),
                              decoration: BoxDecoration(
                                color: Colors.white.withOpacity(0.2),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  const Icon(
                                    Icons.wb_sunny_outlined,
                                    size: 14,
                                    color: Colors.white,
                                  ),
                                  const SizedBox(width: 4),
                                  Text(
                                    catchData.weatherConditions!,
                                    style: const TextStyle(
                                      fontSize: 12,
                                      color: Colors.white,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ],
                      ),
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 构建装备展示内容
  Widget _buildEquipmentContent(MomentVo moment) {
    final equipmentData =
        MomentTypeDataParser.parseEquipment(moment.typeSpecificData);
    if (equipmentData == null) return const SizedBox.shrink();

    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 12, 16, 0),
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.deepOrange.shade400.withOpacity(0.85),
              Colors.orange.shade600.withOpacity(0.9),
            ],
          ),
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.orange.shade400.withOpacity(0.3),
              blurRadius: 12,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Stack(
          children: [
            // 装饰性背景
            Positioned(
              right: -20,
              bottom: -20,
              child: Icon(
                Icons.shopping_bag,
                size: 80,
                color: Colors.white.withOpacity(0.1),
              ),
            ),
            // 内容
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: const Icon(
                          Icons.shopping_bag,
                          size: 20,
                          color: Colors.white,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          equipmentData.equipmentName ?? '装备展示',
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      if (equipmentData.rating != null)
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 10, vertical: 4),
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.9),
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              ...List.generate(5, (index) {
                                return Icon(
                                  index < equipmentData.rating!
                                      ? Icons.star
                                      : Icons.star_border,
                                  size: 14,
                                  color: Colors.amber.shade600,
                                );
                              }),
                            ],
                          ),
                        ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  // 装备信息网格
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.95),
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          blurRadius: 6,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Column(
                      children: [
                        if (equipmentData.category != null ||
                            equipmentData.brand != null)
                          Row(
                            children: [
                              if (equipmentData.category != null)
                                Expanded(
                                  child: _buildEquipmentInfoItem(
                                    icon: Icons.category,
                                    label: '类别',
                                    value: equipmentData.category!,
                                    color: Colors.orange.shade600,
                                  ),
                                ),
                              if (equipmentData.category != null &&
                                  equipmentData.brand != null)
                                Container(
                                  width: 1,
                                  height: 40,
                                  color: Colors.grey.shade200,
                                  margin: const EdgeInsets.symmetric(
                                      horizontal: 12),
                                ),
                              if (equipmentData.brand != null)
                                Expanded(
                                  child: _buildEquipmentInfoItem(
                                    icon: Icons.business,
                                    label: '品牌',
                                    value: equipmentData.brand!,
                                    color: Colors.orange.shade600,
                                  ),
                                ),
                            ],
                          ),
                        if ((equipmentData.category != null ||
                                equipmentData.brand != null) &&
                            (equipmentData.model != null ||
                                equipmentData.price != null))
                          const Divider(height: 20),
                        if (equipmentData.model != null ||
                            equipmentData.price != null)
                          Row(
                            children: [
                              if (equipmentData.model != null)
                                Expanded(
                                  child: _buildEquipmentInfoItem(
                                    icon: Icons.model_training,
                                    label: '型号',
                                    value: equipmentData.model!,
                                    color: Colors.orange.shade600,
                                  ),
                                ),
                              if (equipmentData.model != null &&
                                  equipmentData.price != null)
                                Container(
                                  width: 1,
                                  height: 40,
                                  color: Colors.grey.shade200,
                                  margin: const EdgeInsets.symmetric(
                                      horizontal: 12),
                                ),
                              if (equipmentData.price != null)
                                Expanded(
                                  child: _buildEquipmentInfoItem(
                                    icon: Icons.attach_money,
                                    label: '价格',
                                    value: equipmentData.price!,
                                    color: Colors.orange.shade600,
                                  ),
                                ),
                            ],
                          ),
                      ],
                    ),
                  ),
                  if (equipmentData.targetFishTypes != null &&
                      equipmentData.targetFishTypes!.isNotEmpty)
                    Padding(
                      padding: const EdgeInsets.only(top: 12),
                      child: Wrap(
                        spacing: 8,
                        runSpacing: 8,
                        children: equipmentData.targetFishTypes!.map((fish) {
                          return Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 12, vertical: 6),
                            decoration: BoxDecoration(
                              color: Colors.white.withOpacity(0.2),
                              borderRadius: BorderRadius.circular(20),
                              border: Border.all(
                                color: Colors.white.withOpacity(0.3),
                                width: 1,
                              ),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  Icons.set_meal,
                                  size: 14,
                                  color: Colors.white.withOpacity(0.9),
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  fish.name ?? "未知",
                                  style: const TextStyle(
                                    fontSize: 12,
                                    color: Colors.white,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ),
                          );
                        }).toList(),
                      ),
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 构建技巧分享内容
  Widget _buildTechniqueContent(MomentVo moment) {
    final techniqueData =
        MomentTypeDataParser.parseTechnique(moment.typeSpecificData);
    if (techniqueData == null) return const SizedBox.shrink();

    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 12, 16, 0),
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.green.shade400.withOpacity(0.85),
              Colors.teal.shade600.withOpacity(0.9),
            ],
          ),
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.green.shade400.withOpacity(0.3),
              blurRadius: 12,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Stack(
          children: [
            // 装饰性背景
            Positioned(
              right: -30,
              top: -30,
              child: Container(
                width: 100,
                height: 100,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: Colors.white.withOpacity(0.1),
                ),
              ),
            ),
            Positioned(
              left: -20,
              bottom: -20,
              child: Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: Colors.white.withOpacity(0.08),
                ),
              ),
            ),
            // 内容
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: const Icon(
                          Icons.lightbulb,
                          size: 20,
                          color: Colors.white,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          techniqueData.techniqueName ?? '技巧分享',
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      if (techniqueData.difficulty != null)
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 12, vertical: 4),
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: _getDifficultyGradient(
                                  techniqueData.difficulty!),
                            ),
                            borderRadius: BorderRadius.circular(20),
                            boxShadow: [
                              BoxShadow(
                                color: _getDifficultyColor(
                                        techniqueData.difficulty!)
                                    .withOpacity(0.3),
                                blurRadius: 6,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Text(
                            techniqueData.difficulty!,
                            style: const TextStyle(
                              fontSize: 12,
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                    ],
                  ),
                  if (techniqueData.description != null)
                    Container(
                      margin: const EdgeInsets.only(top: 12),
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.15),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: Colors.white.withOpacity(0.2),
                          width: 1,
                        ),
                      ),
                      child: Text(
                        techniqueData.description!,
                        style: const TextStyle(
                          fontSize: 13,
                          color: Colors.white,
                          height: 1.4,
                        ),
                        maxLines: 3,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      if (techniqueData.environments != null &&
                          techniqueData.environments!.isNotEmpty)
                        Expanded(
                          child: Container(
                            padding: const EdgeInsets.all(10),
                            decoration: BoxDecoration(
                              color: Colors.white.withOpacity(0.9),
                              borderRadius: BorderRadius.circular(12),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withOpacity(0.1),
                                  blurRadius: 6,
                                  offset: const Offset(0, 2),
                                ),
                              ],
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    Icon(
                                      Icons.landscape,
                                      size: 16,
                                      color: Colors.green.shade700,
                                    ),
                                    const SizedBox(width: 6),
                                    Text(
                                      '适用环境',
                                      style: TextStyle(
                                        fontSize: 12,
                                        fontWeight: FontWeight.w600,
                                        color: Colors.green.shade700,
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 6),
                                Wrap(
                                  spacing: 4,
                                  children:
                                      techniqueData.environments!.map((env) {
                                    return Container(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 8, vertical: 2),
                                      decoration: BoxDecoration(
                                        color: Colors.green.shade50,
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      child: Text(
                                        env,
                                        style: TextStyle(
                                          fontSize: 11,
                                          color: Colors.green.shade800,
                                        ),
                                      ),
                                    );
                                  }).toList(),
                                ),
                              ],
                            ),
                          ),
                        ),
                    ],
                  ),
                  if (techniqueData.targetFishTypes != null &&
                      techniqueData.targetFishTypes!.isNotEmpty)
                    Padding(
                      padding: const EdgeInsets.only(top: 8),
                      child: Wrap(
                        spacing: 8,
                        runSpacing: 8,
                        children: techniqueData.targetFishTypes!.map((fish) {
                          return Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 12, vertical: 6),
                            decoration: BoxDecoration(
                              color: Colors.white.withOpacity(0.2),
                              borderRadius: BorderRadius.circular(20),
                              border: Border.all(
                                color: Colors.white.withOpacity(0.3),
                                width: 1,
                              ),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  Icons.set_meal,
                                  size: 14,
                                  color: Colors.white.withOpacity(0.9),
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  fish.name ?? "未知",
                                  style: const TextStyle(
                                    fontSize: 12,
                                    color: Colors.white,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ),
                          );
                        }).toList(),
                      ),
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 构建问答求助内容
  Widget _buildQuestionContent(MomentVo moment) {
    final questionData =
        MomentTypeDataParser.parseQuestion(moment.typeSpecificData);
    if (questionData == null) return const SizedBox.shrink();

    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 12, 16, 0),
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.purple.shade400.withOpacity(0.85),
              Colors.deepPurple.shade600.withOpacity(0.9),
            ],
          ),
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.purple.shade400.withOpacity(0.3),
              blurRadius: 12,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Stack(
          children: [
            // 装饰性背景 - 问号图案
            Positioned(
              right: -15,
              top: -15,
              child: Transform.rotate(
                angle: -0.2,
                child: Text(
                  '?',
                  style: TextStyle(
                    fontSize: 80,
                    fontWeight: FontWeight.bold,
                    color: Colors.white.withOpacity(0.1),
                  ),
                ),
              ),
            ),
            // 内容
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 标题行
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: const Icon(
                          Icons.help_outline,
                          size: 20,
                          color: Colors.white,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              '问题求助',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.white70,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            const SizedBox(height: 2),
                            Text(
                              questionData.questionTitle ?? '求助问题',
                              style: const TextStyle(
                                fontSize: 15,
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                              ),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  // 问题详情
                  if (questionData.detailedProblem != null)
                    Container(
                      margin: const EdgeInsets.only(top: 12),
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            Colors.white.withOpacity(0.95),
                            Colors.white.withOpacity(0.9),
                          ],
                        ),
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.1),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(
                                Icons.description_outlined,
                                size: 14,
                                color: Colors.purple.shade700,
                              ),
                              const SizedBox(width: 6),
                              Text(
                                '问题描述',
                                style: TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.w600,
                                  color: Colors.purple.shade700,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 6),
                          Text(
                            questionData.detailedProblem!,
                            style: TextStyle(
                              fontSize: 13,
                              color: Colors.grey.shade800,
                              height: 1.4,
                            ),
                            maxLines: 3,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    ),
                  // 标签
                  if (questionData.tags != null &&
                      questionData.tags!.isNotEmpty)
                    Padding(
                      padding: const EdgeInsets.only(top: 12),
                      child: Wrap(
                        spacing: 8,
                        runSpacing: 8,
                        children: questionData.tags!.map((tag) {
                          return Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 14, vertical: 6),
                            decoration: BoxDecoration(
                              color: Colors.white.withOpacity(0.2),
                              borderRadius: BorderRadius.circular(20),
                              border: Border.all(
                                color: Colors.white.withOpacity(0.3),
                                width: 1,
                              ),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Container(
                                  width: 4,
                                  height: 4,
                                  decoration: const BoxDecoration(
                                    color: Colors.white,
                                    shape: BoxShape.circle,
                                  ),
                                ),
                                const SizedBox(width: 6),
                                Text(
                                  tag,
                                  style: const TextStyle(
                                    fontSize: 12,
                                    color: Colors.white,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ),
                          );
                        }).toList(),
                      ),
                    ),
                  // 底部提示
                  Container(
                    margin: const EdgeInsets.only(top: 12),
                    padding:
                        const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.15),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.chat_bubble_outline,
                          size: 14,
                          color: Colors.white.withOpacity(0.9),
                        ),
                        const SizedBox(width: 6),
                        Text(
                          '点击查看回答',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.white.withOpacity(0.9),
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 辅助方法：构建装备信息项（新设计）
  Widget _buildEquipmentInfoItem({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Column(
      children: [
        Icon(icon, size: 20, color: color),
        const SizedBox(height: 4),
        Text(
          label,
          style: TextStyle(
            fontSize: 11,
            color: Colors.grey.shade600,
          ),
        ),
        const SizedBox(height: 2),
        Text(
          value,
          style: TextStyle(
            fontSize: 13,
            fontWeight: FontWeight.w600,
            color: Colors.grey.shade800,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
      ],
    );
  }

  // 获取难度对应的渐变色
  List<Color> _getDifficultyGradient(String difficulty) {
    switch (difficulty) {
      case '入门级':
        return [Colors.green.shade400, Colors.green.shade600];
      case '进阶级':
        return [Colors.orange.shade400, Colors.orange.shade600];
      case '专家级':
        return [Colors.red.shade400, Colors.red.shade600];
      default:
        return [Colors.grey.shade400, Colors.grey.shade600];
    }
  }

  // 获取难度对应的颜色
  Color _getDifficultyColor(String difficulty) {
    switch (difficulty) {
      case '入门级':
        return Colors.green;
      case '进阶级':
        return Colors.orange;
      case '专家级':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  Widget _buildImageGrid(List<dynamic> images) {
    final displayImages = images.take(3).toList();
    final remainingCount = images.length - 3;

    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 12, 16, 0),
      child: LayoutBuilder(
        builder: (context, constraints) {
          final width = constraints.maxWidth;
          final imageSize = (width - 8) / 3;

          if (images.length == 1) {
            return ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: CachedNetworkImage(
                imageUrl: images[0].imageUrl ?? images[0],
                width: width,
                height: width * 0.6,
                fit: BoxFit.cover,
              ),
            );
          }

          return Row(
            children: displayImages.asMap().entries.map((entry) {
              final index = entry.key;
              final image = entry.value;
              final isLast = index == displayImages.length - 1;

              return Padding(
                padding: EdgeInsets.only(
                    right: index < displayImages.length - 1 ? 4 : 0),
                child: Stack(
                  children: [
                    ClipRRect(
                      borderRadius: BorderRadius.circular(8),
                      child: CachedNetworkImage(
                        imageUrl: image.imageUrl ?? image,
                        width: imageSize,
                        height: imageSize,
                        fit: BoxFit.cover,
                      ),
                    ),
                    if (isLast && remainingCount > 0)
                      Positioned.fill(
                        child: Container(
                          decoration: BoxDecoration(
                            color: Colors.black.withOpacity(0.5),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Center(
                            child: Text(
                              '+$remainingCount',
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ),
                      ),
                  ],
                ),
              );
            }).toList(),
          );
        },
      ),
    );
  }

  Widget _buildInteractionButton({
    required IconData icon,
    int? count,
    Color? color,
    VoidCallback? onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(20),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        child: Row(
          children: [
            Icon(icon, size: 20, color: color ?? Colors.grey.shade600),
            if (count != null && count > 0) ...[
              const SizedBox(width: 4),
              Text(
                count.toString(),
                style: TextStyle(
                  fontSize: 14,
                  color: color ?? Colors.grey.shade600,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingIndicator() {
    return const Padding(
      padding: EdgeInsets.all(16),
      child: Center(
        child: CircularProgressIndicator(strokeWidth: 2),
      ),
    );
  }

  Widget _buildNoMoreDataIndicator() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Text(
          '没有更多动态了',
          style: TextStyle(
            color: Colors.grey.shade500,
            fontSize: 14,
          ),
        ),
      ),
    );
  }

  void _defaultMomentTap(MomentVo moment) async {
    final result = await Navigator.of(context).push<MomentVo>(
      MaterialPageRoute(
        builder: (context) => MomentDetailPage(
          momentId: moment.id!,
          initialMoment: moment,
        ),
      ),
    );
  }

  Color _getMomentTypeColor(String type) {
    switch (type) {
      case 'fishing_catch':
        return Colors.blue.shade600;
      case 'equipment':
        return Colors.orange.shade600;
      case 'technique':
        return Colors.green.shade600;
      case 'question':
        return Colors.purple.shade600;
      default:
        return Colors.grey;
    }
  }

  IconData _getMomentTypeIcon(String type) {
    switch (type) {
      case 'fishing_catch':
        return Icons.catching_pokemon;
      case 'equipment':
        return Icons.shopping_bag;
      case 'technique':
        return Icons.lightbulb;
      case 'question':
        return Icons.help;
      default:
        return Icons.article;
    }
  }

  String _formatTimeAgo(String? createdAt) {
    if (createdAt == null) return '';

    try {
      final dateTime = DateTime.parse(createdAt);
      return DateTimeUtil.formatTime(dateTime);
    } catch (e) {
      return createdAt;
    }
  }
}
