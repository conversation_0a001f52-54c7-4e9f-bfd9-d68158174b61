                        -H/opt/homebrew/Caskroom/flutter/3.19.2/flutter/packages/flutter_tools/gradle/src/main/groovy
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=28
-DANDROID_PLATFORM=android-28
-DANDROID_ABI=armeabi-v7a
-DCMAKE_ANDROID_ARCH_ABI=armeabi-v7a
-DANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/28.0.12433566
-DCMAKE_ANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/28.0.12433566
-DC<PERSON>KE_TOOLCHAIN_FILE=/Users/<USER>/Library/Android/sdk/ndk/28.0.12433566/build/cmake/android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=/Users/<USER>/Documents/yunmiaokeji/user-app-flutter/user_app/build/app/intermediates/cxx/Debug/422r1b4p/obj/armeabi-v7a
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=/Users/<USER>/Documents/yunmiaokeji/user-app-flutter/user_app/build/app/intermediates/cxx/Debug/422r1b4p/obj/armeabi-v7a
-DCMAKE_BUILD_TYPE=Debug
-B/Users/<USER>/Documents/yunmiaokeji/user-app-flutter/user_app/android/app/.cxx/Debug/422r1b4p/armeabi-v7a
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2